<?php
/**
 * لوحة تحكم المشتريات - النظام الجديد
 * يتبع النظام الحديث بدون أكواد HTML مباشرة
 */

// إعداد بطاقات الإحصائيات للنظام المختلط
$stats_cards = [
    [
        'title' => 'إجمالي الموردين',
        'value' => $stats['total_suppliers'] ?? 0,
        'icon' => 'fas fa-truck',
        'color' => 'primary',
        'col_size' => 3
    ],
    [
        'title' => 'الموردين النشطين',
        'value' => $stats['active_suppliers'] ?? 0,
        'icon' => 'fas fa-check-circle',
        'color' => 'success',
        'col_size' => 3
    ],
    [
        'title' => 'مجموعات الموردين',
        'value' => $stats['total_groups'] ?? 0,
        'icon' => 'fas fa-folder-open',
        'color' => 'info',
        'col_size' => 3
    ],
    [
        'title' => 'موردين غير نشطين',
        'value' => $stats['inactive_suppliers'] ?? 0,
        'icon' => 'fas fa-times-circle',
        'color' => 'warning',
        'col_size' => 3
    ]
];

// إعداد الإجراءات السريعة كبطاقات إضافية
$actions = [
    [
        'type' => 'primary',
        'url' => 'purchases/suppliers/create',
        'icon' => 'fas fa-plus-circle',
        'text' => 'إضافة مورد جديد'
    ],
    [
        'type' => 'success',
        'url' => 'purchases/supplier-groups/create',
        'icon' => 'fas fa-folder-plus',
        'text' => 'إضافة مجموعة موردين'
    ],
    [
        'type' => 'info',
        'url' => 'purchases/suppliers',
        'icon' => 'fas fa-list',
        'text' => 'عرض جميع الموردين'
    ],
    [
        'type' => 'warning',
        'url' => 'purchases/supplier-groups',
        'icon' => 'fas fa-folder-open',
        'text' => 'عرض مجموعات الموردين'
    ]
];

// إعداد أعمدة جدول آخر الموردين
$recent_suppliers_columns = [
    [
        'field' => 'entity_number',
        'title' => 'الرقم',
        'type' => 'text',
        'width' => '100px'
    ],
    [
        'field' => 'G_name_ar',
        'title' => 'اسم المورد',
        'type' => 'link',
        'url' => 'purchases/suppliers/{entity_number}',
        'subtitle_field' => 'G_name_en'
    ],
    [
        'field' => 'S_company_name',
        'title' => 'اسم الشركة',
        'type' => 'text'
    ],
    [
        'field' => 'group_name',
        'title' => 'المجموعة',
        'type' => 'badge',
        'default_value' => '-'
    ],
    [
        'field' => 'G_status',
        'title' => 'الحالة',
        'type' => 'badge',
        'status_config' => [
            'classes' => [
                'active' => 'success',
                'inactive' => 'secondary',
                'suspended' => 'warning',
                'draft' => 'info'
            ],
            'texts' => [
                'active' => 'نشط',
                'inactive' => 'غير نشط',
                'suspended' => 'معلق',
                'draft' => 'مسودة'
            ]
        ]
    ],
    [
        'field' => 'created_at',
        'title' => 'تاريخ الإضافة',
        'type' => 'date'
    ],
    [
        'field' => 'actions',
        'title' => 'الإجراءات',
        'type' => 'actions',
        'width' => '120px',
        'buttons' => [
            [
                'type' => 'link',
                'url' => 'purchases/suppliers/{entity_number}',
                'class' => 'btn-primary',
                'icon' => 'fas fa-eye',
                'title' => 'عرض'
            ],
            [
                'type' => 'link',
                'url' => 'purchases/suppliers/{entity_number}/edit',
                'class' => 'btn-success',
                'icon' => 'fas fa-edit',
                'title' => 'تعديل'
            ]
        ]
    ]
];

// إعداد Empty State لآخر الموردين
$recent_suppliers_empty_state = [
    'icon' => 'fas fa-truck',
    'message' => 'لا توجد موردين مضافين حتى الآن',
    'action' => [
        'url' => 'purchases/suppliers/create',
        'text' => 'إضافة مورد جديد'
    ]
];

// إعداد breadcrumb
$breadcrumb = [
    ['title' => 'المشتريات', 'active' => true]
];

// استخدام النظام المختلط (بطاقات + جدول)
render_page([
    'title' => $title ?? 'لوحة تحكم المشتريات',
    'breadcrumb' => $breadcrumb,
    'module' => 'purchases',
    'entity' => 'dashboard',
    'stats' => $stats_cards,
    'actions' => $actions,
    'columns' => $recent_suppliers_columns,
    'data' => $recentSuppliers ?? [],
    'empty_state' => $recent_suppliers_empty_state,
    'pagination' => ['total_items' => 0] // إخفاء pagination
]);
?>
