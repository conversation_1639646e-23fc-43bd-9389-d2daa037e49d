<?php
/**
 * لوحة تحكم المشتريات - النظام الجديد
 * يتبع النظام الحديث بدون أكواد HTML مباشرة
 */

// إعداد بطاقات الإحصائيات للنظام المختلط
$stats_cards = [
    [
        'title' => 'إجمالي الموردين',
        'value' => $stats['total_suppliers'] ?? 0,
        'icon' => 'fas fa-truck',
        'color' => 'primary',
        'col_size' => 3
    ],
    [
        'title' => 'الموردين النشطين',
        'value' => $stats['active_suppliers'] ?? 0,
        'icon' => 'fas fa-check-circle',
        'color' => 'success',
        'col_size' => 3
    ],
    [
        'title' => 'مجموعات الموردين',
        'value' => $stats['total_groups'] ?? 0,
        'icon' => 'fas fa-folder-open',
        'color' => 'info',
        'col_size' => 3
    ],
    [
        'title' => 'موردين غير نشطين',
        'value' => $stats['inactive_suppliers'] ?? 0,
        'icon' => 'fas fa-times-circle',
        'color' => 'warning',
        'col_size' => 3
    ]
];

// إعداد الإجراءات السريعة كقسم منفصل
$quick_actions_section = [
    'title' => 'الإجراءات السريعة',
    'type' => 'actions_grid',
    'actions' => [
        [
            'title' => 'إضافة مورد جديد',
            'url' => 'purchases/suppliers/create',
            'icon' => 'fas fa-plus-circle',
            'color' => 'primary',
            'col_size' => 3
        ],
        [
            'title' => 'إضافة مجموعة موردين',
            'url' => 'purchases/supplier-groups/create',
            'icon' => 'fas fa-folder-plus',
            'color' => 'success',
            'col_size' => 3
        ],
        [
            'title' => 'عرض جميع الموردين',
            'url' => 'purchases/suppliers',
            'icon' => 'fas fa-list',
            'color' => 'info',
            'col_size' => 3
        ],
        [
            'title' => 'عرض مجموعات الموردين',
            'url' => 'purchases/supplier-groups',
            'icon' => 'fas fa-folder-open',
            'color' => 'warning',
            'col_size' => 3
        ]
    ]
];

// إعداد الإجراءات للـ toolbar (فارغة لأننا نستخدم sections)
$actions = [];

// إعداد أعمدة جدول آخر الموردين
$recent_suppliers_columns = [
    [
        'field' => 'entity_number',
        'title' => 'الرقم',
        'type' => 'text',
        'width' => '100px'
    ],
    [
        'field' => 'G_name_ar',
        'title' => 'اسم المورد',
        'type' => 'link',
        'url' => 'purchases/suppliers/{entity_number}',
        'subtitle_field' => 'G_name_en'
    ],
    [
        'field' => 'S_company_name',
        'title' => 'اسم الشركة',
        'type' => 'text'
    ],
    [
        'field' => 'group_name',
        'title' => 'المجموعة',
        'type' => 'badge',
        'default_value' => '-'
    ],
    [
        'field' => 'G_status',
        'title' => 'الحالة',
        'type' => 'badge',
        'status_config' => [
            'classes' => [
                'active' => 'success',
                'inactive' => 'secondary',
                'suspended' => 'warning',
                'draft' => 'info'
            ],
            'texts' => [
                'active' => 'نشط',
                'inactive' => 'غير نشط',
                'suspended' => 'معلق',
                'draft' => 'مسودة'
            ]
        ]
    ],
    [
        'field' => 'created_at',
        'title' => 'تاريخ الإضافة',
        'type' => 'date'
    ],
    [
        'field' => 'actions',
        'title' => 'الإجراءات',
        'type' => 'actions',
        'width' => '120px',
        'buttons' => [
            [
                'type' => 'link',
                'url' => 'purchases/suppliers/{entity_number}',
                'class' => 'btn-primary',
                'icon' => 'fas fa-eye',
                'title' => 'عرض'
            ],
            [
                'type' => 'link',
                'url' => 'purchases/suppliers/{entity_number}/edit',
                'class' => 'btn-success',
                'icon' => 'fas fa-edit',
                'title' => 'تعديل'
            ]
        ]
    ]
];

// إعداد Empty State لآخر الموردين
$recent_suppliers_empty_state = [
    'icon' => 'fas fa-truck',
    'message' => 'لا توجد موردين مضافين حتى الآن',
    'action' => [
        'url' => 'purchases/suppliers/create',
        'text' => 'إضافة مورد جديد'
    ]
];

// إعداد breadcrumb
$breadcrumb = [
    ['title' => 'المشتريات', 'active' => true]
];

// بناء المحتوى المخصص
ob_start();
?>

<!-- بطاقات الإحصائيات -->
<?php if (function_exists('render_stats_cards')): ?>
    <?php render_stats_cards($stats_cards); ?>
<?php endif; ?>

<!-- قسم الإجراءات السريعة -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="header-title"><?= $quick_actions_section['title'] ?></h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php foreach ($quick_actions_section['actions'] as $action): ?>
                        <div class="col-md-<?= $action['col_size'] ?>">
                            <div class="d-grid mb-2">
                                <a href="<?= base_url($action['url']) ?>" class="btn btn-<?= $action['color'] ?>">
                                    <i class="<?= $action['icon'] ?> me-2"></i>
                                    <?= $action['title'] ?>
                                </a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- قسم آخر الموردين -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="header-title">آخر الموردين المضافين</h4>
            </div>
            <div class="card-body">
                <?php if (empty($recentSuppliers)): ?>
                    <div class="text-center py-4">
                        <i class="<?= $recent_suppliers_empty_state['icon'] ?> fa-3x text-muted mb-3"></i>
                        <p class="text-muted"><?= $recent_suppliers_empty_state['message'] ?></p>
                        <a href="<?= base_url($recent_suppliers_empty_state['action']['url']) ?>"
                           class="btn btn-primary btn-sm">
                            <?= $recent_suppliers_empty_state['action']['text'] ?>
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <?php if (function_exists('render_datatable_table')): ?>
                            <?php render_datatable_table($recent_suppliers_columns, $recentSuppliers, $recent_suppliers_empty_state, 'purchases', 'suppliers'); ?>
                        <?php endif; ?>
                    </div>

                    <div class="text-center mt-3">
                        <a href="<?= base_url('purchases/suppliers') ?>" class="btn btn-light">
                            عرض جميع الموردين
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();

// استخدام النظام البسيط
render_page([
    'title' => $title ?? 'لوحة تحكم المشتريات',
    'breadcrumb' => $breadcrumb,
    'content' => $content
]);
?>
