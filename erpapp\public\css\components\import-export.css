/**
 * Import Export Components Styles
 * أنماط مكونات الاستيراد والتصدير
 */

/* ========================================
   Toolbar Buttons
======================================== */

.toolbar-right {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: auto;
}

.datatable-export-btn,
.datatable-import-btn,
.datatable-template-btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    background: #ffffff;
    color: #374151;
    font-size: 13px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
    cursor: pointer;
    white-space: nowrap;
}

.datatable-export-btn:hover,
.datatable-import-btn:hover,
.datatable-template-btn:hover {
    background: #f9fafb;
    border-color: #d1d5db;
    color: #111827;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.datatable-export-btn {
    border-color: #10b981;
    color: #10b981;
}

.datatable-export-btn:hover {
    background: #ecfdf5;
    border-color: #059669;
    color: #059669;
}

.datatable-import-btn {
    border-color: #3b82f6;
    color: #3b82f6;
}

.datatable-import-btn:hover {
    background: #eff6ff;
    border-color: #2563eb;
    color: #2563eb;
}

.datatable-template-btn {
    border-color: #f59e0b;
    color: #f59e0b;
}

.datatable-template-btn:hover {
    background: #fffbeb;
    border-color: #d97706;
    color: #d97706;
}

/* ========================================
   Import Export Modals
======================================== */

.import-export-modal .modal-dialog {
    max-width: 600px;
}

.import-export-modal .modal-dialog.modal-lg {
    max-width: 800px;
}

.import-export-modal .modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.import-export-modal .modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;
    padding: 20px 24px;
    border-bottom: none;
}

.import-export-modal .modal-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.import-export-modal .modal-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s ease;
}

.import-export-modal .modal-close:hover {
    background: rgba(255, 255, 255, 0.3);
}

.import-export-modal .modal-body {
    padding: 24px;
}

.import-export-modal .modal-footer {
    padding: 16px 24px;
    border-top: 1px solid #e5e7eb;
    background: #f9fafb;
    border-radius: 0 0 12px 12px;
}

/* ========================================
   Export Options
======================================== */

.export-options {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 16px;
    margin-top: 8px;
}

.export-options .form-check {
    margin-bottom: 12px;
    padding-left: 24px;
}

.export-options .form-check:last-child {
    margin-bottom: 0;
}

.export-options .form-check-input {
    margin-top: 2px;
}

.export-options .form-check-label {
    font-weight: 500;
    color: #374151;
    cursor: pointer;
}

/* ========================================
   Import Steps
======================================== */

.import-step {
    margin-bottom: 24px;
}

.step-title {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
}

.step-number {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    background: #3b82f6;
    color: white;
    border-radius: 50%;
    font-size: 14px;
    font-weight: 600;
}

/* ========================================
   File Upload Area
======================================== */

.file-upload-area {
    margin-bottom: 20px;
}

.upload-zone {
    border: 2px dashed #d1d5db;
    border-radius: 12px;
    padding: 40px 20px;
    text-align: center;
    background: #fafafa;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-zone:hover {
    border-color: #3b82f6;
    background: #eff6ff;
}

.upload-zone.dragover {
    border-color: #10b981;
    background: #ecfdf5;
}

.upload-zone i {
    font-size: 48px;
    color: #9ca3af;
    margin-bottom: 16px;
    display: block;
}

.upload-zone p {
    font-size: 16px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 8px;
}

.upload-zone small {
    color: #6b7280;
    font-size: 13px;
}

/* ========================================
   Upload Progress
======================================== */

.upload-progress {
    text-align: center;
}

.upload-progress .progress {
    height: 8px;
    background: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 12px;
}

.upload-progress .progress-bar {
    background: linear-gradient(90deg, #3b82f6, #10b981);
    height: 100%;
    transition: width 0.3s ease;
}

.upload-progress .progress-text {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
}

/* ========================================
   Column Mapping
======================================== */

.column-mapping {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 20px;
}

.mapping-row {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;
    padding: 12px;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
}

.mapping-row:last-child {
    margin-bottom: 0;
}

.mapping-label {
    flex: 1;
    font-weight: 500;
    color: #374151;
}

.mapping-label.required::after {
    content: " *";
    color: #ef4444;
}

.mapping-select {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: white;
    font-size: 14px;
}

.mapping-arrow {
    color: #9ca3af;
    font-size: 16px;
}

/* ========================================
   Preview Container
======================================== */

.preview-container {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 20px;
}

.preview-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px;
    background: #eff6ff;
    border: 1px solid #bfdbfe;
    border-radius: 6px;
}

.preview-stats {
    display: flex;
    gap: 24px;
}

.preview-stat {
    text-align: center;
}

.preview-stat-number {
    display: block;
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
}

.preview-stat-label {
    font-size: 12px;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.preview-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.preview-table th,
.preview-table td {
    padding: 12px;
    text-align: right;
    border-bottom: 1px solid #e5e7eb;
    font-size: 13px;
}

.preview-table th {
    background: #f3f4f6;
    font-weight: 600;
    color: #374151;
}

.preview-table td {
    color: #6b7280;
}

.preview-table tr:last-child td {
    border-bottom: none;
}

/* ========================================
   Error Messages
======================================== */

.import-errors {
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 6px;
    padding: 16px;
    margin-top: 16px;
}

.import-errors h6 {
    color: #dc2626;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
}

.import-errors ul {
    margin: 0;
    padding-right: 20px;
}

.import-errors li {
    color: #991b1b;
    font-size: 13px;
    margin-bottom: 4px;
}

/* ========================================
   Responsive Design
======================================== */

@media (max-width: 768px) {
    .toolbar-right {
        flex-direction: column;
        gap: 4px;
        width: 100%;
    }
    
    .datatable-export-btn,
    .datatable-import-btn,
    .datatable-template-btn {
        width: 100%;
        justify-content: center;
    }
    
    .import-export-modal .modal-dialog {
        margin: 10px;
        max-width: calc(100% - 20px);
    }
    
    .mapping-row {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }
    
    .preview-stats {
        flex-direction: column;
        gap: 12px;
    }
}
