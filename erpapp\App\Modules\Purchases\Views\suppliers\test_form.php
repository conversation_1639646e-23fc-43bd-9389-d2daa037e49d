<?php
/**
 * اختبار النظام الديناميكي للنماذج
 */

// تحديد التبويبات والحقول ديناميكياً - نسخة مبسطة للاختبار
$form_tabs = [
    [
        'tab_id' => 'basic',
        'title' => 'البيانات الأساسية',
        'icon' => 'mdi mdi-account-circle',
        'fields' => [
            [
                'name' => 'G_name_ar',
                'label' => 'اسم المورد بالعربية',
                'type' => 'text',
                'required' => true,
                'placeholder' => 'أدخل اسم المورد'
            ],
            [
                'name' => 'G_name_en',
                'label' => 'اسم المورد بالإنجليزية',
                'type' => 'text',
                'placeholder' => 'أدخل اسم المورد بالإنجليزية'
            ]
        ]
    ],
    [
        'tab_id' => 'contact',
        'title' => 'معلومات الاتصال',
        'icon' => 'mdi mdi-phone',
        'fields' => [
            [
                'name' => 'G_phone',
                'label' => 'رقم الهاتف',
                'type' => 'tel',
                'placeholder' => 'أدخل رقم الهاتف'
            ],
            [
                'name' => 'S_email',
                'label' => 'البريد الإلكتروني',
                'type' => 'email',
                'placeholder' => 'أدخل البريد الإلكتروني'
            ]
        ]
    ]
];

// اختبار النظام
echo "<h1>اختبار النظام الديناميكي</h1>";

// التحقق من وجود الدالة
if (function_exists('render_form_page')) {
    echo "<p style='color: green;'>✅ دالة render_form_page موجودة</p>";
    
    try {
        // استخدام النظام الموحد
        render_form_page([
            'title' => 'اختبار النموذج الديناميكي',
            'module' => 'purchases',
            'entity' => 'suppliers',
            'breadcrumb' => [
                ['title' => 'المشتريات', 'url' => 'purchases'],
                ['title' => 'الموردين', 'url' => 'purchases/suppliers'],
                ['title' => 'اختبار النموذج', 'active' => true]
            ],
            'actions' => [
                [
                    'type' => 'secondary',
                    'url' => 'purchases/suppliers',
                    'icon' => 'fas fa-arrow-left',
                    'text' => 'العودة للقائمة'
                ],
                [
                    'type' => 'primary',
                    'form' => 'testForm',
                    'icon' => 'fas fa-save',
                    'text' => 'حفظ البيانات',
                    'submit' => true
                ]
            ],
            'form' => [
                'action' => 'purchases/suppliers/store',
                'method' => 'POST',
                'id' => 'testForm',
                'tabs' => $form_tabs
            ]
        ]);
        
        echo "<p style='color: green;'>✅ تم تنفيذ render_form_page بنجاح</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في تنفيذ render_form_page: " . $e->getMessage() . "</p>";
    }
    
} else {
    echo "<p style='color: red;'>❌ دالة render_form_page غير موجودة</p>";
    echo "<p>الدوال المتاحة:</p>";
    echo "<pre>";
    $functions = get_defined_functions()['user'];
    foreach ($functions as $func) {
        if (strpos($func, 'render') !== false) {
            echo "- $func\n";
        }
    }
    echo "</pre>";
}
?>
