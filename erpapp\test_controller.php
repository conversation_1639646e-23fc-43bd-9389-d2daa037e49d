<?php
/**
 * اختبار تحميل المكتبة في المتحكم
 */

define('BASE_PATH', __DIR__);

// تحميل الملفات المطلوبة
require_once 'loader.php';

echo "<h2>اختبار تحميل المكتبة في المتحكم</h2>";

try {
    // محاكاة تحميل المتحكم
    require_once 'App/Modules/Purchases/Controllers/ImportExportController.php';
    
    echo "<p>✓ تم تحميل المتحكم بنجاح</p>";
    
    // إنشاء كائن من المتحكم
    $controller = new \App\Modules\Purchases\Controllers\ImportExportController();
    
    echo "<p>✓ تم إنشاء كائن المتحكم بنجاح</p>";
    
    // فحص حالة المكتبة
    if (class_exists('PhpOffice\PhpSpreadsheet\Spreadsheet')) {
        echo "<p style='color: green;'>✓ مكتبة PhpSpreadsheet محملة في المتحكم!</p>";
    } else {
        echo "<p style='color: orange;'>⚠ مكتبة PhpSpreadsheet غير محملة في المتحكم</p>";
    }
    
    echo "<p><strong>النتيجة:</strong> المتحكم جاهز للاستخدام!</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ خطأ: " . $e->getMessage() . "</p>";
} catch (Error $e) {
    echo "<p style='color: red;'>✗ خطأ فادح: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p>الآن جرب الاستيراد من الواجهة الرئيسية!</p>";
?>
