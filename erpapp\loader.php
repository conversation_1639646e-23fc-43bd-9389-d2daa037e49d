<?php
/**
 * Application Loader
 *
 * This file is responsible for loading the application and its dependencies.
 * It is used to load the application from the root directory.
 */

// Define base path
define('BASE_PATH', __DIR__);

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Load configuration
require_once BASE_PATH . '/config/config.php';
require_once BASE_PATH . '/config/database.php';

// Load helpers
require_once BASE_PATH . '/App/Helpers/functions.php';
require_once BASE_PATH . '/App/Helpers/email.php';
require_once BASE_PATH . '/App/Helpers/route_helpers.php';
require_once BASE_PATH . '/App/Helpers/notifications.php';
require_once BASE_PATH . '/App/Helpers/css_helper.php';
require_once BASE_PATH . '/App/Helpers/js_helper.php';
require_once BASE_PATH . '/App/Helpers/security_helper.php';
require_once BASE_PATH . '/App/Helpers/security_monitor.php';
require_once BASE_PATH . '/App/Helpers/permissions.php';
require_once BASE_PATH . '/App/Helpers/sidebar_helpers.php';
require_once BASE_PATH . '/App/Helpers/security_cache.php';
require_once BASE_PATH . '/App/Helpers/filter_helper.php';
require_once BASE_PATH . '/App/Helpers/datatable_helper.php';
require_once BASE_PATH . '/App/Helpers/page_helper.php';
require_once BASE_PATH . '/App/Helpers/cards_helper.php';
require_once BASE_PATH . '/App/Helpers/import_export_helper.php';
// Set default timezone
date_default_timezone_set(DEFAULT_TIMEZONE);

// Set security headers
set_security_headers();

// فحص محاولات الاختراق
if (detect_intrusion_attempts()) {
    http_response_code(403);
    die('Access Denied');
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Load Composer autoloader (ضروري للمكتبات الخارجية)
$composer_autoloader = dirname(BASE_PATH) . '../vendor/autoload.php';
if (file_exists($composer_autoloader)) {
    require_once $composer_autoloader;
}

// Load custom autoloader
require_once BASE_PATH . '/App/Core/autoload.php';

// Load core classes
require_once BASE_PATH . '/App/Core/Module.php';
require_once BASE_PATH . '/App/Core/ModuleRouter.php';
require_once BASE_PATH . '/App/Core/ExceptionHandler.php';

// Register exception handler
\App\Core\ExceptionHandler::register();

// Load legacy router for backward compatibility
require_once BASE_PATH . '/App/Core/Router.php';
$router = new Router();

// Load legacy routes for backward compatibility
require_once BASE_PATH . '/App/Routes/web.php';

// Load modules
load_modules();

// Check if the request should be handled by the module router
$moduleRouter = \App\Core\ModuleRouter::getInstance();
$url = $_SERVER['REQUEST_URI'];
$method = $_SERVER['REQUEST_METHOD'];

// Remove base path from URL
$base_path = parse_url(APP_URL, PHP_URL_PATH);
if ($base_path && strpos($url, $base_path) === 0) {
    $url = substr($url, strlen($base_path));
}

// التحقق من الصلاحيات قبل التوجيه
PermissionManager::checkCurrentRoute();

// If the module router can handle the request, use it
if ($moduleRouter->match($url, $method)) {
    $moduleRouter->dispatch();
} else {
    // Otherwise, use the legacy router
    $router->dispatch();
}
