/**
 * نظام الإشعارات الموحد - مُصحح
 */
class NotificationSystem {
    constructor() {
        this.container = null;
        this.notifications = [];
        this.counter = 0;

        // إضافة نظام الصوت
        this.sounds = {
            success: new Audio('/erpapp/public/sounds/success.mp3'),
            info: new Audio('/erpapp/public/sounds/info.mp3'),
            warning: new Audio('/erpapp/public/sounds/warning.mp3'),
            error: new Audio('/erpapp/public/sounds/error.mp3'),
            create: new Audio('/erpapp/public/sounds/create.mp3'),
            update: new Audio('/erpapp/public/sounds/update.mp3'),
            delete: new Audio('/erpapp/public/sounds/delete.mp3'),
            save: new Audio('/erpapp/public/sounds/save.mp3'),
            default: new Audio('/erpapp/public/sounds/default.mp3')
        };

        this.soundEnabled = false;
        this.notificationQueue = [];
        this.isProcessing = false;

        this.init();
        this.initSoundSettings();
    }

    /**
     * تهيئة نظام الإشعارات
     */
    init() {
        // إنشاء حاوية الإشعارات إذا لم تكن موجودة
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.className = 'notification-container';

            // إضافة دعم RTL
            if (document.dir === 'rtl' || document.documentElement.lang === 'ar') {
                this.container.classList.add('rtl');
            }

            document.body.appendChild(this.container);
        }
    }

    /**
     * تهيئة إعدادات الصوت
     */
    async initSoundSettings() {
        try {
            const saved = localStorage.getItem('soundNotifications');
            if (saved !== null) {
                this.soundEnabled = JSON.parse(saved);
            } else {
                // جلب من الخادم
                const response = await fetch('/erpapp/api/user/sound-settings');
                const data = await response.json();
                this.soundEnabled = data.enabled || false;
            }
        } catch (error) {
            console.error('Error loading sound settings:', error);
            this.soundEnabled = false;
        }
    }

    /**
     * إنشاء إشعار جديد
     */
    create(options) {
        const defaults = {
            type: 'info',
            title: '',
            message: '',
            duration: 5000,
            icon: this.getDefaultIcon(options.type || 'info')
        };

        const settings = { ...defaults, ...options };
        const id = ++this.counter;

        // إنشاء عنصر الإشعار
        const notification = document.createElement('div');
        notification.className = `notification notification-${settings.type}`;
        notification.dataset.id = id;

        // إنشاء محتوى الإشعار
        notification.innerHTML = `
            <div class="notification-icon">
                <i class="${settings.icon}"></i>
            </div>
            <div class="notification-content">
                <div class="notification-title">${settings.title}</div>
                <div class="notification-message">${settings.message}</div>
            </div>
            <div class="notification-close">
                <i class="fas fa-times"></i>
            </div>
            <div class="notification-progress">
                <div class="notification-progress-bar" style="animation-duration: ${settings.duration}ms"></div>
            </div>
        `;

        // إضافة الإشعار إلى الحاوية
        this.container.appendChild(notification);
        this.notifications.push({ id, element: notification, timeout: null });

        // إضافة معالج النقر لزر الإغلاق
        const closeButton = notification.querySelector('.notification-close');
        closeButton.addEventListener('click', () => this.close(id));

        // إظهار الإشعار بعد إضافته للـ DOM
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        // إغلاق الإشعار تلقائيًا بعد المدة المحددة
        if (settings.duration > 0) {
            const timeout = setTimeout(() => {
                this.close(id);
            }, settings.duration);

            // تحديث الـ timeout في قائمة الإشعارات
            const index = this.notifications.findIndex(n => n.id === id);
            if (index !== -1) {
                this.notifications[index].timeout = timeout;
            }
        }

        return id;
    }

    /**
     * إغلاق إشعار محدد
     */
    close(id) {
        const index = this.notifications.findIndex(n => n.id === id);
        if (index === -1) return;

        const { element, timeout } = this.notifications[index];

        // إلغاء الـ timeout إذا كان موجودًا
        if (timeout) {
            clearTimeout(timeout);
        }

        // إخفاء الإشعار
        element.classList.remove('show');

        // إزالة الإشعار من الـ DOM بعد انتهاء التأثير
        setTimeout(() => {
            if (element.parentNode) {
                element.parentNode.removeChild(element);
            }
            this.notifications.splice(index, 1);
        }, 400);
    }

    /**
     * إغلاق جميع الإشعارات
     */
    closeAll() {
        [...this.notifications].forEach(notification => {
            this.close(notification.id);
        });
    }

    /**
     * الحصول على الأيقونة الافتراضية حسب نوع الإشعار
     */
    getDefaultIcon(type) {
        switch (type) {
            case 'success':
                return 'fas fa-check';
            case 'error':
                return 'fas fa-times';
            case 'warning':
                return 'fas fa-exclamation';
            case 'info':
            default:
                return 'fas fa-info';
        }
    }

    /**
     * الحصول على عنوان حسب النوع
     */
    getTypeTitle(type) {
        switch (type) {
            case 'success': return 'نجاح';
            case 'error': return 'خطأ';
            case 'warning': return 'تحذير';
            case 'info': 
            default: return 'معلومات';
        }
    }

    /**
     * تشغيل الصوت حسب نوع الإشعار
     */
    playSound(type = 'default') {
        if (!this.soundEnabled) return;

        const sound = this.sounds[type] || this.sounds.default;
        sound.volume = 0.5;
        
        sound.play().catch(error => {
            console.warn('Could not play notification sound:', error);
        });
    }

    /**
     * إنشاء إشعار نجاح
     */
    success(message, playSound = true) {
        if (playSound) this.playSound('success');
        return this.create({
            type: 'success',
            title: 'نجاح',
            message,
            duration: 5000
        });
    }

    /**
     * إنشاء إشعار خطأ
     */
    error(message, playSound = true) {
        if (playSound) this.playSound('error');
        return this.create({
            type: 'error',
            title: 'خطأ',
            message,
            duration: 5000
        });
    }

    /**
     * إنشاء إشعار تحذير
     */
    warning(message, playSound = true) {
        if (playSound) this.playSound('warning');
        return this.create({
            type: 'warning',
            title: 'تحذير',
            message,
            duration: 5000
        });
    }

    /**
     * إنشاء إشعار معلومات
     */
    info(message, playSound = true) {
        if (playSound) this.playSound('info');
        return this.create({
            type: 'info',
            title: 'معلومات',
            message,
            duration: 5000
        });
    }

    /**
     * إشعار إنشاء جديد
     */
    createNotification(message, playSound = true) {
        if (playSound) this.playSound('create');
        return this.create({
            type: 'success',
            title: 'تم الإنشاء',
            message,
            duration: 5000
        });
    }

    /**
     * إشعار تحديث
     */
    update(message, playSound = true) {
        if (playSound) this.playSound('update');
        return this.create({
            type: 'success',
            title: 'تم التحديث',
            message,
            duration: 5000
        });
    }

    /**
     * إشعار حذف
     */
    delete(message, playSound = true) {
        if (playSound) this.playSound('delete');
        return this.create({
            type: 'warning',
            title: 'تم الحذف',
            message,
            duration: 5000
        });
    }

    /**
     * إشعار حفظ
     */
    save(message, playSound = true) {
        if (playSound) this.playSound('save');
        return this.create({
            type: 'success',
            title: 'تم الحفظ',
            message,
            duration: 5000
        });
    }

    /**
     * تحديث إعدادات الصوت
     */
    updateSoundSettings(enabled) {
        this.soundEnabled = enabled;
        localStorage.setItem('soundNotifications', JSON.stringify(enabled));
        this.updateServerSoundSettings(enabled);
    }

    /**
     * تحديث إعدادات الصوت في الخادم
     */
    async updateServerSoundSettings(enabled) {
        try {
            await fetch('/erpapp/api/user/sound-settings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ enabled: enabled })
            });
        } catch (error) {
            console.error('Error updating sound settings:', error);
        }
    }

    /**
     * بدء مراقبة الإشعارات الجديدة
     */
    startPolling(interval = 30000) {
        setInterval(() => {
            this.processServerNotifications();
        }, interval);
    }

    /**
     * معالجة الإشعارات من الخادم
     */
    async processServerNotifications() {
        try {
            const response = await fetch('/erpapp/api/notifications/unread');
            const data = await response.json();

            if (data.success && data.notifications) {
                data.notifications.forEach(notification => {
                    this.processNotification(notification);
                });
            }
        } catch (error) {
            console.error('Error fetching notifications:', error);
        }
    }

    /**
     * معالجة إشعار واحد
     */
    processNotification(notification) {
        const data = JSON.parse(notification.data_json || '{}');

        // عرض الإشعار
        this.create({
            type: data.type || 'info',
            title: notification.title,
            message: notification.message,
            duration: 5000
        });

        // تشغيل الصوت إذا كان مفعل
        if (data.sound_enabled && this.soundEnabled) {
            this.playSound(data.sound_type || 'default');
        }

        // تحديد الإشعار كمقروء
        this.markAsRead(notification.notification_id);
    }

    /**
     * تحديد الإشعار كمقروء
     */
    async markAsRead(notificationId) {
        try {
            await fetch(`/erpapp/api/notifications/${notificationId}/read`, {
                method: 'POST'
            });
        } catch (error) {
            console.error('Error marking notification as read:', error);
        }
    }
}

// إنشاء كائن عام للإشعارات
const notifications = new NotificationSystem();

// إضافة دعم للرسائل المخزنة في الجلسة
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة النظام العام
    window.notificationSystem = notifications;
    
    // بدء مراقبة الإشعارات الجديدة
    notifications.startPolling();
    
    // التحقق من وجود رسائل مخزنة في الجلسة
    if (window.flashMessages) {
        window.flashMessages.forEach(function(message) {
            if (message.type && message.text) {
                switch (message.type) {
                    case 'success':
                        notifications.success(message.text);
                        break;
                    case 'danger':
                    case 'error':
                        notifications.error(message.text);
                        break;
                    case 'warning':
                        notifications.warning(message.text);
                        break;
                    case 'info':
                    default:
                        notifications.info(message.text);
                        break;
                }
            }
        });
    }
});

// دوال عامة للاستخدام السهل
window.playSuccessSound = () => notifications.playSound('success');
window.playErrorSound = () => notifications.playSound('error');
window.playCreateSound = () => notifications.playSound('create');
window.playUpdateSound = () => notifications.playSound('update');
window.playDeleteSound = () => notifications.playSound('delete');
window.playSaveSound = () => notifications.playSound('save');

// دوال الإشعارات مع الأصوات
window.notifySuccess = (message) => notifications.success(message);
window.notifyError = (message) => notifications.error(message);
window.notifyCreate = (message) => notifications.createNotification(message);
window.notifyUpdate = (message) => notifications.update(message);
window.notifyDelete = (message) => notifications.delete(message);
window.notifySave = (message) => notifications.save(message);
