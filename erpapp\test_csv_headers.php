<?php
/**
 * اختبار مخصص لمشكلة رؤوس أعمدة CSV
 */

require_once 'config/config.php';
require_once 'App/Helpers/csv_helper.php';

echo "<h2>🔍 اختبار مشكلة رؤوس أعمدة CSV</h2>";

// إنشاء ملف CSV تجريبي بطرق مختلفة
echo "<h3>1. اختبار أنواع مختلفة من ملفات CSV</h3>";

// النوع الأول: ملف عادي
$csv1 = "اسم المورد,اسم الشركة,الهاتف,البريد الإلكتروني\n";
$csv1 .= "شركة الأمثال,شركة الأمثال المحدودة,0112345678,<EMAIL>\n";

// النوع الثاني: ملف مع BOM
$csv2 = "\xEF\xBB\xBF" . "اسم المورد,اسم الشركة,الهاتف,البريد الإلكتروني\n";
$csv2 .= "شركة الأمثال,شركة الأمثال المحدودة,0112345678,<EMAIL>\n";

// النوع الثالث: ملف مع مسافات إضافية
$csv3 = " اسم المورد , اسم الشركة , الهاتف , البريد الإلكتروني \n";
$csv3 .= "شركة الأمثال,شركة الأمثال المحدودة,0112345678,<EMAIL>\n";

// النوع الرابع: ملف بالفاصلة المنقوطة (مثل ملفك)
$csv4 = "اسم المورد;رقم المجموعة;الهاتف;الجوال\n";
$csv4 .= "شركة الأمثال;1;0112345678;0501234567\n";

$test_files = [
    'عادي' => $csv1,
    'مع BOM' => $csv2,
    'مع مسافات' => $csv3,
    'فاصلة منقوطة' => $csv4
];

foreach ($test_files as $type => $content) {
    echo "<h4>اختبار ملف CSV ($type)</h4>";
    
    $test_file = sys_get_temp_dir() . '/test_' . md5($type) . '.csv';
    file_put_contents($test_file, $content);
    
    echo "<p><strong>المحتوى الخام:</strong></p>";
    echo "<pre>" . htmlspecialchars($content) . "</pre>";
    
    // قراءة الملف
    $result = read_csv_file($test_file);
    
    echo "<p><strong>نتيجة القراءة:</strong></p>";
    if ($result['success']) {
        echo "<p>✅ نجح</p>";
        echo "<p><strong>الرؤوس:</strong></p>";
        echo "<ul>";
        foreach ($result['headers'] as $i => $header) {
            echo "<li>[$i] = '" . htmlspecialchars($header) . "' (طول: " . strlen($header) . ")</li>";
        }
        echo "</ul>";
        
        echo "<p><strong>JSON للرؤوس:</strong></p>";
        echo "<pre>" . json_encode($result['headers'], JSON_UNESCAPED_UNICODE) . "</pre>";
        
    } else {
        echo "<p>❌ فشل: " . $result['error'] . "</p>";
    }
    
    unlink($test_file);
    echo "<hr>";
}

// اختبار محاكاة الاستجابة من الخادم
echo "<h3>2. محاكاة استجابة الخادم</h3>";

$test_headers = ['اسم المورد', 'اسم الشركة', 'الهاتف', 'البريد الإلكتروني'];

$response = [
    'success' => true,
    'message' => 'تم رفع الملف بنجاح',
    'headers' => $test_headers,
    'rows_count' => 1,
    'available_columns' => [
        ['field' => 'G_name_ar', 'title' => 'اسم المورد', 'required' => true],
        ['field' => 'S_company_name', 'title' => 'اسم الشركة', 'required' => false],
        ['field' => 'G_phone', 'title' => 'الهاتف', 'required' => false],
        ['field' => 'S_email', 'title' => 'البريد الإلكتروني', 'required' => false]
    ]
];

echo "<p><strong>الاستجابة المرسلة إلى JavaScript:</strong></p>";
echo "<pre>" . json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "</pre>";

// اختبار JavaScript محاكي
echo "<h3>3. اختبار JavaScript محاكي</h3>";
?>

<script>
// محاكاة البيانات المستلمة
const testData = <?= json_encode($response, JSON_UNESCAPED_UNICODE) ?>;

console.log('البيانات المستلمة:', testData);
console.log('رؤوس الأعمدة:', testData.headers);
console.log('نوع رؤوس الأعمدة:', typeof testData.headers);
console.log('هل هي مصفوفة؟', Array.isArray(testData.headers));

// معالجة الرؤوس
let headers = testData.headers;
if (typeof headers === 'string') {
    console.log('تحويل النص إلى مصفوفة...');
    headers = headers.split(',').map(h => h.trim());
    console.log('بعد التحويل:', headers);
} else {
    console.log('الرؤوس مصفوفة بالفعل');
}

// إنشاء جدول تطابق الأعمدة
function createTestMappingTable() {
    let html = '<table class="table table-bordered"><thead><tr><th>عمود الملف</th><th>حقل النظام</th></tr></thead><tbody>';
    
    headers.forEach((header, index) => {
        html += '<tr>';
        html += '<td><strong>' + header + '</strong></td>';
        html += '<td><select class="form-select">';
        html += '<option value="">-- اختر حقل --</option>';
        
        testData.available_columns.forEach(column => {
            html += '<option value="' + column.field + '">' + column.title + '</option>';
        });
        
        html += '</select></td>';
        html += '</tr>';
    });
    
    html += '</tbody></table>';
    
    document.getElementById('testMappingTable').innerHTML = html;
}

// تشغيل الاختبار عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    createTestMappingTable();
});
</script>

<?php
echo "<h4>جدول التطابق المحاكي:</h4>";
echo "<div id='testMappingTable'></div>";

echo "<h3>🎯 التشخيص</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<p><strong>إذا كان الجدول أعلاه يعرض الأعمدة بشكل صحيح، فالمشكلة في:</strong></p>";
echo "<ul>";
echo "<li>🔍 <strong>ملف CSV الأصلي</strong> - قد يحتوي على أحرف خاصة أو تنسيق غير صحيح</li>";
echo "<li>🔗 <strong>طلب AJAX</strong> - قد لا يرسل البيانات بشكل صحيح</li>";
echo "<li>📡 <strong>استجابة الخادم</strong> - قد تحتوي على أخطاء في التنسيق</li>";
echo "</ul>";

echo "<p><strong>إذا كان الجدول لا يعرض الأعمدة بشكل صحيح، فالمشكلة في:</strong></p>";
echo "<ul>";
echo "<li>💻 <strong>JavaScript</strong> - في معالجة البيانات</li>";
echo "<li>🎨 <strong>CSS</strong> - في عرض الجدول</li>";
echo "<li>🌐 <strong>المتصفح</strong> - مشكلة في التوافق</li>";
echo "</ul>";
echo "</div>";

echo "<h3>📋 خطوات الحل</h3>";
echo "<ol>";
echo "<li><strong>افتح أدوات المطور (F12)</strong></li>";
echo "<li><strong>اذهب إلى تبويب Console</strong></li>";
echo "<li><strong>ابحث عن الرسائل</strong> التي تبدأ بـ 'البيانات المستلمة'</li>";
echo "<li><strong>تحقق من نوع البيانات</strong> - هل هي مصفوفة أم نص؟</li>";
echo "<li><strong>إذا كانت نص</strong> - فالمشكلة في قراءة CSV</li>";
echo "<li><strong>إذا كانت مصفوفة</strong> - فالمشكلة في عرض الجدول</li>";
echo "</ol>";

echo "<h3>🔧 الحلول المقترحة</h3>";
echo "<div style='background: #e7f3ff; padding: 15px; border-left: 4px solid #007bff;'>";
echo "<h4>إذا كانت المشكلة في قراءة CSV:</h4>";
echo "<ul>";
echo "<li>تأكد من أن الملف محفوظ بترميز UTF-8</li>";
echo "<li>تأكد من أن الفواصل هي فواصل عادية (,)</li>";
echo "<li>تأكد من عدم وجود أحرف خاصة في أسماء الأعمدة</li>";
echo "</ul>";

echo "<h4>إذا كانت المشكلة في JavaScript:</h4>";
echo "<ul>";
echo "<li>تحديث المتصفح إلى أحدث إصدار</li>";
echo "<li>مسح cache المتصفح</li>";
echo "<li>تجربة متصفح آخر</li>";
echo "</ul>";
echo "</div>";
?>
