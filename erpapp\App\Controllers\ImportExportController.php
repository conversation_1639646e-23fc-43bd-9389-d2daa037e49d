<?php
namespace App\Controllers;

use Exception;

/**
 * ImportExportController - متحكم الاستيراد والتصدير
 */
class ImportExportController
{
    /**
     * Route parameters
     */
    protected $params = [];

    /**
     * Constructor
     */
    public function __construct($params = [])
    {
        $this->params = $params;

        // التحقق من تسجيل الدخول
        if (!is_logged_in()) {
            redirect(base_url('login'));
        }

        // التحقق من وجود شركة حالية
        $user = current_user();
        if (!$user || !$user['current_company_id']) {
            flash('error', 'يجب تحديد شركة حالية للوصول إلى الاستيراد والتصدير', 'warning');
            redirect(base_url('companies'));
        }
    }

    /**
     * تصدير البيانات
     */
    public function export()
    {
        try {
            $module = $_POST['module'] ?? '';
            $entity = $_POST['entity'] ?? '';
            $export_type = $_POST['export_type'] ?? 'current'; // current, all, filtered
            $include_filters = isset($_POST['include_filters']);

            if (empty($module) || empty($entity)) {
                throw new Exception('معاملات غير صحيحة');
            }

            // الحصول على إعدادات التصدير للوحدة والكيان
            $export_config = $this->getExportConfig($module, $entity);
            if (!$export_config) {
                throw new Exception('التصدير غير مدعوم لهذا الكيان');
            }

            // الحصول على البيانات
            $data = $this->getExportData($module, $entity, $export_type);
            
            if (empty($data)) {
                throw new Exception('لا توجد بيانات للتصدير');
            }

            // إعداد التصدير
            $config = [
                'title' => $export_config['title'],
                'filename' => $export_config['filename'] . '_' . date('Y-m-d_H-i-s'),
                'include_filters' => $include_filters,
                'filters' => $include_filters ? ($_SESSION['filters'][$entity] ?? []) : []
            ];

            // تصدير البيانات
            $result = export_data_to_excel($data, $export_config['columns'], $config);

            if ($result['success']) {
                // تنزيل الملف
                download_file($result['filepath'], $result['filename']);
                exit;
            } else {
                throw new Exception($result['error']);
            }

        } catch (Exception $e) {
            flash('error', 'خطأ في التصدير: ' . $e->getMessage());
            redirect($_SERVER['HTTP_REFERER'] ?? base_url());
        }
    }

    /**
     * تنزيل قالب الاستيراد
     */
    public function downloadTemplate()
    {
        try {
            $module = $_GET['module'] ?? '';
            $entity = $_GET['entity'] ?? '';

            if (empty($module) || empty($entity)) {
                throw new Exception('معاملات غير صحيحة');
            }

            // الحصول على إعدادات الاستيراد
            $import_config = $this->getImportConfig($module, $entity);
            if (!$import_config) {
                throw new Exception('الاستيراد غير مدعوم لهذا الكيان');
            }

            // إنشاء القالب
            $config = [
                'title' => 'قالب استيراد ' . $import_config['title'],
                'filename' => 'template_' . $entity . '_' . date('Y-m-d_H-i-s')
            ];

            $result = create_import_template($import_config['columns'], $config);

            if ($result['success']) {
                download_file($result['filepath'], $result['filename']);
                exit;
            } else {
                throw new Exception($result['error']);
            }

        } catch (Exception $e) {
            flash('error', 'خطأ في إنشاء القالب: ' . $e->getMessage());
            redirect($_SERVER['HTTP_REFERER'] ?? base_url());
        }
    }

    /**
     * رفع ملف للاستيراد
     */
    public function uploadFile()
    {
        try {
            if (!isset($_FILES['import_file'])) {
                throw new Exception('لم يتم رفع أي ملف');
            }

            $file = $_FILES['import_file'];
            $module = $_POST['module'] ?? '';
            $entity = $_POST['entity'] ?? '';

            if (empty($module) || empty($entity)) {
                throw new Exception('معاملات غير صحيحة');
            }

            // التحقق من نوع الملف
            $allowed_types = ['xlsx', 'xls'];
            $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            
            if (!in_array($file_extension, $allowed_types)) {
                throw new Exception('نوع الملف غير مدعوم. يرجى رفع ملف Excel (.xlsx أو .xls)');
            }

            // التحقق من حجم الملف (5MB كحد أقصى)
            if ($file['size'] > 5 * 1024 * 1024) {
                throw new Exception('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت');
            }

            // نقل الملف إلى مجلد مؤقت
            $upload_dir = sys_get_temp_dir();
            $filename = 'import_' . uniqid() . '.' . $file_extension;
            $filepath = $upload_dir . '/' . $filename;

            if (!move_uploaded_file($file['tmp_name'], $filepath)) {
                throw new Exception('فشل في رفع الملف');
            }

            // قراءة الملف
            $read_result = read_excel_file($filepath);
            
            if (!$read_result['success']) {
                unlink($filepath); // حذف الملف
                throw new Exception('خطأ في قراءة الملف: ' . $read_result['error']);
            }

            // حفظ معلومات الملف في الجلسة
            $_SESSION['import_file'] = [
                'filepath' => $filepath,
                'filename' => $file['name'],
                'module' => $module,
                'entity' => $entity,
                'headers' => $read_result['headers'],
                'data' => $read_result['data'],
                'rows_count' => $read_result['rows_count'],
                'uploaded_at' => time()
            ];

            // إرجاع النتيجة كـ JSON
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => 'تم رفع الملف بنجاح',
                'headers' => $read_result['headers'],
                'rows_count' => $read_result['rows_count']
            ]);
            exit;

        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
            exit;
        }
    }

    /**
     * معاينة البيانات المستوردة
     */
    public function previewImport()
    {
        try {
            if (!isset($_SESSION['import_file'])) {
                throw new Exception('لا يوجد ملف مرفوع');
            }

            $import_file = $_SESSION['import_file'];
            $mapping = $_POST['mapping'] ?? [];

            if (empty($mapping)) {
                throw new Exception('يجب تحديد تطابق الأعمدة');
            }

            // الحصول على إعدادات الاستيراد
            $import_config = $this->getImportConfig($import_file['module'], $import_file['entity']);
            if (!$import_config) {
                throw new Exception('الاستيراد غير مدعوم لهذا الكيان');
            }

            // معالجة التطابق
            $mapping_result = process_column_mapping(
                $import_file['headers'],
                $import_config['columns'],
                $mapping
            );

            if (!$mapping_result['success']) {
                throw new Exception(implode('<br>', $mapping_result['errors']));
            }

            // معاينة البيانات (أول 10 صفوف)
            $preview_data = array_slice($import_file['data'], 0, 10);
            
            $validation_result = import_data_with_validation(
                $preview_data,
                $mapping,
                $import_config['columns']
            );

            // إرجاع النتيجة
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'preview_data' => $validation_result['data'],
                'total_rows' => $import_file['rows_count'],
                'preview_rows' => count($preview_data),
                'errors' => $validation_result['errors']
            ]);
            exit;

        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
            exit;
        }
    }

    /**
     * تنفيذ الاستيراد
     */
    public function executeImport()
    {
        try {
            if (!isset($_SESSION['import_file'])) {
                throw new Exception('لا يوجد ملف مرفوع');
            }

            $import_file = $_SESSION['import_file'];
            $mapping = $_POST['mapping'] ?? [];

            if (empty($mapping)) {
                throw new Exception('يجب تحديد تطابق الأعمدة');
            }

            // الحصول على إعدادات الاستيراد
            $import_config = $this->getImportConfig($import_file['module'], $import_file['entity']);
            if (!$import_config) {
                throw new Exception('الاستيراد غير مدعوم لهذا الكيان');
            }

            // استيراد البيانات مع التحقق
            $validation_result = import_data_with_validation(
                $import_file['data'],
                $mapping,
                $import_config['columns'],
                $import_config['validation_callback'] ?? null
            );

            if (!$validation_result['success']) {
                throw new Exception('فشل في التحقق من البيانات:<br>' . implode('<br>', $validation_result['errors']));
            }

            // حفظ البيانات في قاعدة البيانات
            $save_result = $this->saveImportedData(
                $import_file['module'],
                $import_file['entity'],
                $validation_result['data']
            );

            if (!$save_result['success']) {
                throw new Exception($save_result['error']);
            }

            // تنظيف الملف المؤقت
            if (file_exists($import_file['filepath'])) {
                unlink($import_file['filepath']);
            }
            unset($_SESSION['import_file']);

            // إرجاع النتيجة
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => 'تم الاستيراد بنجاح',
                'imported_rows' => $save_result['imported_rows'],
                'total_rows' => $validation_result['total_rows']
            ]);
            exit;

        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
            exit;
        }
    }

    /**
     * الحصول على إعدادات التصدير للوحدة والكيان
     */
    private function getExportConfig($module, $entity)
    {
        $configs = [
            'purchases' => [
                'suppliers' => [
                    'title' => 'الموردين',
                    'filename' => 'suppliers_export',
                    'columns' => [
                        ['field' => 'entity_number', 'title' => 'الرقم', 'export' => true],
                        ['field' => 'G_name_ar', 'title' => 'اسم المورد', 'export' => true],
                        ['field' => 'G_name_en', 'title' => 'الاسم الإنجليزي', 'export' => true],
                        ['field' => 'S_company_name', 'title' => 'اسم الشركة', 'export' => true],
                        ['field' => 'group_name', 'title' => 'المجموعة', 'export' => true],
                        ['field' => 'G_phone', 'title' => 'الهاتف', 'export' => true],
                        ['field' => 'G_mobile', 'title' => 'الجوال', 'export' => true],
                        ['field' => 'S_email', 'title' => 'البريد الإلكتروني', 'export' => true],
                        ['field' => 'G_status', 'title' => 'الحالة', 'export' => true, 'type' => 'badge',
                         'status_config' => [
                             'texts' => ['active' => 'نشط', 'inactive' => 'غير نشط', 'suspended' => 'معلق']
                         ]],
                        ['field' => 'actions', 'title' => 'الإجراءات', 'export' => false]
                    ]
                ]
            ]
        ];

        return $configs[$module][$entity] ?? null;
    }

    /**
     * الحصول على إعدادات الاستيراد للوحدة والكيان
     */
    private function getImportConfig($module, $entity)
    {
        $configs = [
            'purchases' => [
                'suppliers' => [
                    'title' => 'الموردين',
                    'columns' => [
                        ['field' => 'G_name_ar', 'title' => 'اسم المورد *', 'required' => true, 'data_type' => 'text', 'max_length' => 255],
                        ['field' => 'G_name_en', 'title' => 'الاسم الإنجليزي', 'required' => false, 'data_type' => 'text', 'max_length' => 255],
                        ['field' => 'S_company_name', 'title' => 'اسم الشركة', 'required' => false, 'data_type' => 'text', 'max_length' => 255],
                        ['field' => 'G_phone', 'title' => 'الهاتف', 'required' => false, 'data_type' => 'text', 'max_length' => 20],
                        ['field' => 'G_mobile', 'title' => 'الجوال', 'required' => false, 'data_type' => 'text', 'max_length' => 20],
                        ['field' => 'S_email', 'title' => 'البريد الإلكتروني', 'required' => false, 'data_type' => 'email'],
                        ['field' => 'G_website', 'title' => 'الموقع الإلكتروني', 'required' => false, 'data_type' => 'url'],
                        ['field' => 'G_status', 'title' => 'الحالة', 'required' => false, 'data_type' => 'select',
                         'import_options' => ['active' => 'نشط', 'inactive' => 'غير نشط', 'suspended' => 'معلق']],
                        ['field' => 'S_tax_number', 'title' => 'الرقم الضريبي', 'required' => false, 'data_type' => 'text', 'max_length' => 50],
                        ['field' => 'S_commercial_register', 'title' => 'السجل التجاري', 'required' => false, 'data_type' => 'text', 'max_length' => 50],
                        ['field' => 'G_notes', 'title' => 'ملاحظات', 'required' => false, 'data_type' => 'text', 'max_length' => 1000]
                    ],
                    'validation_callback' => [$this, 'validateSupplierData']
                ]
            ]
        ];

        return $configs[$module][$entity] ?? null;
    }

    /**
     * الحصول على البيانات للتصدير
     */
    private function getExportData($module, $entity, $export_type)
    {
        $company_id = current_user()['current_company_id'];

        switch ($module) {
            case 'purchases':
                switch ($entity) {
                    case 'suppliers':
                        $supplierModel = new \App\Modules\Purchases\Models\Supplier();

                        $filters = [];
                        if ($export_type === 'filtered' && isset($_SESSION['filters']['suppliers'])) {
                            $filters = $_SESSION['filters']['suppliers'];
                            unset($filters['per_page'], $filters['current_page'], $filters['limit'], $filters['offset']);
                        }

                        return $supplierModel->getByCompany($company_id, $filters);
                }
                break;
        }

        return [];
    }

    /**
     * حفظ البيانات المستوردة
     */
    private function saveImportedData($module, $entity, $data)
    {
        try {
            $company_id = current_user()['current_company_id'];
            $user_id = current_user()['UserID'];

            switch ($module) {
                case 'purchases':
                    switch ($entity) {
                        case 'suppliers':
                            return $this->saveSuppliers($data, $company_id, $user_id);
                    }
                    break;
            }

            return ['success' => false, 'error' => 'نوع البيانات غير مدعوم'];

        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * حفظ الموردين المستوردين
     */
    private function saveSuppliers($suppliers, $company_id, $user_id)
    {
        try {
            $supplierModel = new \App\Modules\Purchases\Models\Supplier();
            $supplierModel->beginTransaction();

            $imported_count = 0;

            foreach ($suppliers as $supplier_data) {
                // إضافة البيانات المطلوبة
                $supplier_data['company_id'] = $company_id;
                $supplier_data['created_by'] = $user_id;
                $supplier_data['G_status'] = $supplier_data['G_status'] ?? 'active';

                // إنشاء المورد
                $result = $supplierModel->create($supplier_data);
                if ($result) {
                    $imported_count++;
                }
            }

            $supplierModel->commit();

            return [
                'success' => true,
                'imported_rows' => $imported_count
            ];

        } catch (Exception $e) {
            $supplierModel->rollback();
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * التحقق من صحة بيانات المورد
     */
    public function validateSupplierData($data, $row_number)
    {
        $errors = [];

        // التحقق من عدم تكرار الاسم
        if (!empty($data['G_name_ar'])) {
            $supplierModel = new \App\Modules\Purchases\Models\Supplier();
            $company_id = current_user()['current_company_id'];

            // هذا يتطلب إضافة دالة للتحقق من التكرار في النموذج
            // $existing = $supplierModel->checkDuplicate($data['G_name_ar'], $company_id);
            // if ($existing) {
            //     $errors[] = "اسم المورد '{$data['G_name_ar']}' موجود مسبقاً في الصف {$row_number}";
            // }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
}
