/**
 * Import Export Components JavaScript
 * سكريبت مكونات الاستيراد والتصدير
 */

// متغيرات عامة
let currentStep = 1;
let uploadedFile = null;
let columnMapping = {};
let systemColumns = [];

/**
 * الحصول على رابط التصدير حسب الوحدة والكيان
 */
function getExportUrl(module, entity) {
    const urls = {
        'purchases': {
            'suppliers': window.APP_CONFIG.APP_URL + '/purchases/suppliers/export'
        }
    };

    return urls[module]?.[entity] || window.APP_CONFIG.APP_URL + '/import-export/export';
}

/**
 * الحصول على رابط رفع الملف حسب الوحدة والكيان
 */
function getUploadUrl(module, entity) {
    const urls = {
        'purchases': {
            'suppliers': window.APP_CONFIG.APP_URL + '/purchases/suppliers/upload-file'
        }
    };

    return urls[module]?.[entity] || window.APP_CONFIG.APP_URL + '/import-export/upload-file';
}

/**
 * الحصول على رابط المعاينة حسب الوحدة والكيان
 */
function getPreviewUrl(module, entity) {
    const urls = {
        'purchases': {
            'suppliers': window.APP_CONFIG.APP_URL + '/purchases/suppliers/preview-import'
        }
    };

    return urls[module]?.[entity] || window.APP_CONFIG.APP_URL + '/import-export/preview-import';
}

/**
 * الحصول على رابط تنفيذ الاستيراد حسب الوحدة والكيان
 */
function getExecuteUrl(module, entity) {
    const urls = {
        'purchases': {
            'suppliers': window.APP_CONFIG.APP_URL + '/purchases/suppliers/execute-import'
        }
    };

    return urls[module]?.[entity] || window.APP_CONFIG.APP_URL + '/import-export/execute-import';
}

/**
 * فتح نافذة التصدير
 */
function openExportModal(module, entity) {
    const modal = document.getElementById('exportModal');
    if (modal) {
        // تحديث action الفورم حسب الوحدة والكيان
        const form = modal.querySelector('#exportForm');
        if (form) {
            form.action = getExportUrl(module, entity);
        }

        modal.style.display = 'flex';
        modal.classList.add('show');
        document.body.classList.add('modal-open');
    }
}

/**
 * فتح نافذة الاستيراد
 */
function openImportModal(module, entity) {
    const modal = document.getElementById('importModal');
    if (modal) {
        // إعادة تعيين الخطوات
        resetImportSteps();
        
        modal.style.display = 'flex';
        modal.classList.add('show');
        document.body.classList.add('modal-open');
        
        // حفظ معلومات الوحدة والكيان
        modal.dataset.module = module;
        modal.dataset.entity = entity;
    }
}

/**
 * إعادة تعيين خطوات الاستيراد
 */
function resetImportSteps() {
    currentStep = 1;
    uploadedFile = null;
    columnMapping = {};
    systemColumns = [];
    
    // إخفاء جميع الخطوات
    document.querySelectorAll('.import-step').forEach(step => {
        step.style.display = 'none';
    });
    
    // إظهار الخطوة الأولى
    document.getElementById('step1').style.display = 'block';
    
    // إعادة تعيين الأزرار
    updateStepButtons();
    
    // مسح محتوى الخطوات
    document.getElementById('columnMapping').innerHTML = '';
    document.getElementById('previewContainer').innerHTML = '';
    
    // إعادة تعيين منطقة الرفع
    const uploadZone = document.querySelector('.upload-zone');
    const uploadProgress = document.getElementById('uploadProgress');
    if (uploadZone && uploadProgress) {
        uploadZone.style.display = 'block';
        uploadProgress.style.display = 'none';
    }
}

/**
 * تحديث أزرار الخطوات
 */
function updateStepButtons() {
    const prevBtn = document.getElementById('prevStep');
    const nextBtn = document.getElementById('nextStep');
    const executeBtn = document.getElementById('executeImport');
    
    // إخفاء جميع الأزرار أولاً
    prevBtn.style.display = 'none';
    nextBtn.style.display = 'none';
    executeBtn.style.display = 'none';
    
    switch (currentStep) {
        case 1:
            // الخطوة الأولى - لا توجد أزرار
            break;
        case 2:
            prevBtn.style.display = 'inline-block';
            nextBtn.style.display = 'inline-block';
            break;
        case 3:
            prevBtn.style.display = 'inline-block';
            executeBtn.style.display = 'inline-block';
            break;
    }
}

/**
 * الانتقال للخطوة التالية
 */
function nextStep() {
    if (currentStep < 3) {
        // إخفاء الخطوة الحالية
        document.getElementById(`step${currentStep}`).style.display = 'none';
        
        currentStep++;
        
        // إظهار الخطوة الجديدة
        document.getElementById(`step${currentStep}`).style.display = 'block';
        
        // تحديث الأزرار
        updateStepButtons();
        
        // تنفيذ إجراءات خاصة بكل خطوة
        if (currentStep === 3) {
            previewImportData();
        }
    }
}

/**
 * الانتقال للخطوة السابقة
 */
function prevStep() {
    if (currentStep > 1) {
        // إخفاء الخطوة الحالية
        document.getElementById(`step${currentStep}`).style.display = 'none';
        
        currentStep--;
        
        // إظهار الخطوة الجديدة
        document.getElementById(`step${currentStep}`).style.display = 'block';
        
        // تحديث الأزرار
        updateStepButtons();
    }
}

/**
 * رفع الملف
 */
function uploadFile(file, module, entity) {
    const formData = new FormData();
    formData.append('import_file', file);

    // إظهار شريط التقدم
    const uploadZone = document.querySelector('.upload-zone');
    const uploadProgress = document.getElementById('uploadProgress');
    uploadZone.style.display = 'none';
    uploadProgress.style.display = 'block';

    fetch(getUploadUrl(module, entity), {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            uploadedFile = {
                headers: data.headers,
                rows_count: data.rows_count
            };
            
            // الانتقال للخطوة التالية
            setTimeout(() => {
                setupColumnMapping(data.headers, module, entity);
                nextStep();
            }, 500);
        } else {
            showError('خطأ في رفع الملف: ' + data.error);
            resetUploadArea();
        }
    })
    .catch(error => {
        showError('خطأ في الاتصال: ' + error.message);
        resetUploadArea();
    });
}

/**
 * إعداد تطابق الأعمدة
 */
function setupColumnMapping(excelHeaders, module, entity) {
    // الحصول على أعمدة النظام (يجب تخصيصها لكل وحدة)
    systemColumns = getSystemColumns(module, entity);
    
    const mappingContainer = document.getElementById('columnMapping');
    let html = '<div class="mapping-header"><h6>ربط أعمدة ملف Excel بأعمدة النظام:</h6></div>';
    
    systemColumns.forEach(column => {
        const isRequired = column.required ? 'required' : '';
        html += `
            <div class="mapping-row">
                <div class="mapping-label ${isRequired}">${column.title}</div>
                <i class="fas fa-arrow-left mapping-arrow"></i>
                <select class="mapping-select" data-field="${column.field}">
                    <option value="">-- اختر العمود --</option>
                    ${excelHeaders.map((header, index) => 
                        `<option value="${index}">${header}</option>`
                    ).join('')}
                </select>
            </div>
        `;
    });
    
    mappingContainer.innerHTML = html;
    
    // إضافة مستمعي الأحداث
    mappingContainer.querySelectorAll('.mapping-select').forEach(select => {
        select.addEventListener('change', function() {
            columnMapping[this.dataset.field] = this.value;
            validateMapping();
        });
    });
}

/**
 * التحقق من صحة التطابق
 */
function validateMapping() {
    const requiredFields = systemColumns.filter(col => col.required).map(col => col.field);
    const mappedRequiredFields = requiredFields.filter(field => columnMapping[field] && columnMapping[field] !== '');
    
    const nextBtn = document.getElementById('nextStep');
    if (mappedRequiredFields.length === requiredFields.length) {
        nextBtn.disabled = false;
        nextBtn.classList.remove('btn-secondary');
        nextBtn.classList.add('btn-primary');
    } else {
        nextBtn.disabled = true;
        nextBtn.classList.remove('btn-primary');
        nextBtn.classList.add('btn-secondary');
    }
}

/**
 * معاينة البيانات المستوردة
 */
function previewImportData() {
    const modal = document.getElementById('importModal');
    const module = modal.dataset.module;
    const entity = modal.dataset.entity;

    fetch(getPreviewUrl(module, entity), {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            mapping: columnMapping
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayPreview(data);
        } else {
            showError('خطأ في المعاينة: ' + data.error);
        }
    })
    .catch(error => {
        showError('خطأ في الاتصال: ' + error.message);
    });
}

/**
 * عرض المعاينة
 */
function displayPreview(data) {
    const container = document.getElementById('previewContainer');
    
    let html = `
        <div class="preview-info">
            <div class="preview-stats">
                <div class="preview-stat">
                    <span class="preview-stat-number">${data.total_rows}</span>
                    <span class="preview-stat-label">إجمالي الصفوف</span>
                </div>
                <div class="preview-stat">
                    <span class="preview-stat-number">${data.preview_rows}</span>
                    <span class="preview-stat-label">صفوف المعاينة</span>
                </div>
                <div class="preview-stat">
                    <span class="preview-stat-number">${data.errors.length}</span>
                    <span class="preview-stat-label">أخطاء</span>
                </div>
            </div>
        </div>
    `;
    
    if (data.preview_data.length > 0) {
        html += '<table class="preview-table"><thead><tr>';
        
        // رؤوس الأعمدة
        systemColumns.forEach(column => {
            if (columnMapping[column.field] !== undefined) {
                html += `<th>${column.title}</th>`;
            }
        });
        
        html += '</tr></thead><tbody>';
        
        // البيانات
        data.preview_data.slice(0, 5).forEach(row => {
            html += '<tr>';
            systemColumns.forEach(column => {
                if (columnMapping[column.field] !== undefined) {
                    const value = row[column.field] || '-';
                    html += `<td>${value}</td>`;
                }
            });
            html += '</tr>';
        });
        
        html += '</tbody></table>';
    }
    
    // عرض الأخطاء إن وجدت
    if (data.errors.length > 0) {
        html += `
            <div class="import-errors">
                <h6>أخطاء في البيانات:</h6>
                <ul>
                    ${data.errors.slice(0, 10).map(error => `<li>${error}</li>`).join('')}
                    ${data.errors.length > 10 ? `<li>... و ${data.errors.length - 10} أخطاء أخرى</li>` : ''}
                </ul>
            </div>
        `;
    }
    
    container.innerHTML = html;
}

/**
 * تنفيذ الاستيراد
 */
function executeImport() {
    const modal = document.getElementById('importModal');
    const module = modal.dataset.module;
    const entity = modal.dataset.entity;

    const executeBtn = document.getElementById('executeImport');
    executeBtn.disabled = true;
    executeBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الاستيراد...';

    fetch(getExecuteUrl(module, entity), {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            mapping: columnMapping
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess(`تم استيراد ${data.imported_rows} من ${data.total_rows} سجل بنجاح`);
            closeModal('importModal');
            // إعادة تحميل الصفحة
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showError('خطأ في الاستيراد: ' + data.error);
        }
    })
    .catch(error => {
        showError('خطأ في الاتصال: ' + error.message);
    })
    .finally(() => {
        executeBtn.disabled = false;
        executeBtn.innerHTML = '<i class="fas fa-check me-1"></i>تنفيذ الاستيراد';
    });
}

/**
 * إعادة تعيين منطقة الرفع
 */
function resetUploadArea() {
    const uploadZone = document.querySelector('.upload-zone');
    const uploadProgress = document.getElementById('uploadProgress');
    uploadZone.style.display = 'block';
    uploadProgress.style.display = 'none';
}

/**
 * الحصول على أعمدة النظام
 */
function getSystemColumns(module, entity) {
    // يجب تخصيص هذا لكل وحدة وكيان
    const configs = {
        purchases: {
            suppliers: [
                { field: 'G_name_ar', title: 'اسم المورد *', required: true },
                { field: 'G_name_en', title: 'الاسم الإنجليزي', required: false },
                { field: 'S_company_name', title: 'اسم الشركة', required: false },
                { field: 'G_phone', title: 'الهاتف', required: false },
                { field: 'G_mobile', title: 'الجوال', required: false },
                { field: 'S_email', title: 'البريد الإلكتروني', required: false },
                { field: 'G_website', title: 'الموقع الإلكتروني', required: false },
                { field: 'G_status', title: 'الحالة', required: false },
                { field: 'S_tax_number', title: 'الرقم الضريبي', required: false },
                { field: 'S_commercial_register', title: 'السجل التجاري', required: false },
                { field: 'G_notes', title: 'ملاحظات', required: false }
            ]
        }
    };
    
    return configs[module]?.[entity] || [];
}

/**
 * إغلاق النافذة المنبثقة
 */
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
        modal.classList.remove('show');
        document.body.classList.remove('modal-open');
    }
}

/**
 * عرض رسالة خطأ
 */
function showError(message) {
    if (typeof toastr !== 'undefined') {
        toastr.error(message);
    } else {
        alert('خطأ: ' + message);
    }
}

/**
 * عرض رسالة نجاح
 */
function showSuccess(message) {
    if (typeof toastr !== 'undefined') {
        toastr.success(message);
    } else {
        alert(message);
    }
}

// تهيئة مستمعي الأحداث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // مستمع رفع الملف
    const importFileInput = document.getElementById('importFile');
    if (importFileInput) {
        importFileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const modal = document.getElementById('importModal');
                const module = modal.dataset.module;
                const entity = modal.dataset.entity;
                uploadFile(file, module, entity);
            }
        });
    }

    // مستمع السحب والإفلات
    const uploadZone = document.querySelector('.upload-zone');
    if (uploadZone) {
        uploadZone.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });

        uploadZone.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
        });

        uploadZone.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                const file = files[0];
                const modal = document.getElementById('importModal');
                const module = modal.dataset.module;
                const entity = modal.dataset.entity;
                uploadFile(file, module, entity);
            }
        });
    }

    // مستمعي أزرار الخطوات
    const prevBtn = document.getElementById('prevStep');
    const nextBtn = document.getElementById('nextStep');
    const executeBtn = document.getElementById('executeImport');

    if (prevBtn) {
        prevBtn.addEventListener('click', prevStep);
    }

    if (nextBtn) {
        nextBtn.addEventListener('click', nextStep);
    }

    if (executeBtn) {
        executeBtn.addEventListener('click', executeImport);
    }

    // مستمعي إغلاق النوافذ
    document.querySelectorAll('.modal-close, [data-dismiss="modal"]').forEach(btn => {
        btn.addEventListener('click', function() {
            const modal = this.closest('.modal');
            if (modal) {
                closeModal(modal.id);
            }
        });
    });

    // إغلاق النافذة عند الضغط خارجها
    document.querySelectorAll('.modal').forEach(modal => {
        modal.addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal(this.id);
            }
        });
    });
});
