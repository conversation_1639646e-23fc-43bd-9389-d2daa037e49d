/**
 * نظام التخطيط الرئيسي - وظائف خاصة بالتخطيط العام
 * Main Layout System - General layout functions
 */

(function() {
    'use strict';

    // انتظار تحميل DOM
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initMainLayout);
    } else {
        initMainLayout();
    }

    function initMainLayout() {
        console.log('🚀 بدء تهيئة نظام التخطيط الرئيسي...');

        // تهيئة جميع مكونات التخطيط
        initScrollbarStyles();
        initCustomScrollbar();
        
        console.log('✅ تم تهيئة نظام التخطيط الرئيسي بنجاح');
    }

    // ===== أنماط شريط التمرير ===== //
    function initScrollbarStyles() {
        // دالة لتطبيق أنماط شريط التمرير حسب اللغة
        function applyScrollbarStyles() {
            const isRTL = document.body.classList.contains('rtl');

            if (isRTL) {
                // تطبيق أنماط للغة العربية (شريط التمرير على اليسار)
                document.documentElement.style.overflowY = 'auto';
                document.documentElement.style.direction = 'rtl';
            } else {
                // تطبيق أنماط للغة الإنجليزية (شريط التمرير على اليمين)
                document.documentElement.style.overflowY = 'auto';
                document.documentElement.style.direction = 'ltr';
            }
        }

        // تطبيق أنماط شريط التمرير عند تحميل الصفحة
        applyScrollbarStyles();
    }

    // ===== شريط التمرير المخصص ===== //
    function initCustomScrollbar() {
        const scrollbarThumb = document.getElementById('scrollbar-thumb');
        const customScrollbar = document.getElementById('custom-scrollbar');

        if (!scrollbarThumb || !customScrollbar) return;

        // حساب نسبة ارتفاع شريط التمرير
        function updateScrollbarThumb() {
            const windowHeight = window.innerHeight;
            const documentHeight = document.documentElement.scrollHeight;

            // التحقق مما إذا كانت هناك حاجة لشريط التمرير
            if (documentHeight <= windowHeight) {
                // إخفاء شريط التمرير إذا لم تكن هناك حاجة له
                customScrollbar.style.display = 'none';
                return;
            } else {
                // إظهار شريط التمرير إذا كانت هناك حاجة له
                customScrollbar.style.display = 'block';
            }

            // حساب نسبة ارتفاع الشريط
            const scrollThumbHeight = (windowHeight / documentHeight) * windowHeight;

            // تعيين الحد الأدنى لارتفاع الشريط
            const minHeight = 30;
            scrollbarThumb.style.height = Math.max(scrollThumbHeight, minHeight) + 'px';

            // تحديث موضع الشريط
            updateScrollbarPosition();
        }

        // تحديث موضع شريط التمرير
        function updateScrollbarPosition() {
            // التحقق مما إذا كانت هناك حاجة لشريط التمرير
            const windowHeight = window.innerHeight;
            const documentHeight = document.documentElement.scrollHeight;

            if (documentHeight <= windowHeight) {
                // إخفاء شريط التمرير إذا لم تكن هناك حاجة له
                customScrollbar.style.display = 'none';
                return;
            } else {
                // إظهار شريط التمرير إذا كانت هناك حاجة له
                customScrollbar.style.display = 'block';
            }

            const scrollPercentage = window.scrollY / (documentHeight - windowHeight);
            const maxScrollTop = windowHeight - parseFloat(scrollbarThumb.style.height || '30px');
            const scrollTop = scrollPercentage * maxScrollTop;

            // تجنب القيم غير الصالحة
            if (isNaN(scrollTop) || !isFinite(scrollTop)) {
                return;
            }

            scrollbarThumb.style.top = scrollTop + 'px';
        }

        // تحديث شريط التمرير عند التمرير
        window.addEventListener('scroll', updateScrollbarPosition);

        // تحديث شريط التمرير عند تغيير حجم النافذة
        window.addEventListener('resize', updateScrollbarThumb);

        // تحديث شريط التمرير عند تحميل المحتوى (الصور، الإطارات، إلخ)
        window.addEventListener('load', updateScrollbarThumb);

        // تحديث شريط التمرير عند تغيير محتوى الصفحة (AJAX، إضافة عناصر ديناميكية)
        const observer = new MutationObserver(function(mutations) {
            updateScrollbarThumb();
        });

        // مراقبة التغييرات في محتوى الصفحة
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['style', 'class']
        });

        // تهيئة شريط التمرير عند تحميل الصفحة
        updateScrollbarThumb();

        // تحديث شريط التمرير بعد فترة قصيرة للتأكد من تحميل جميع العناصر
        setTimeout(updateScrollbarThumb, 500);

        // إضافة تفاعل للشريط
        scrollbarThumb.addEventListener('mouseenter', function() {
            this.style.opacity = '0.9';
        });

        scrollbarThumb.addEventListener('mouseleave', function() {
            this.style.opacity = '0.7';
        });

        // تعديل موضع الشريط حسب اللغة
        const isRTL = document.body.classList.contains('rtl') || document.documentElement.dir === 'rtl';
        if (isRTL) {
            scrollbarThumb.style.right = 'auto';
            scrollbarThumb.style.left = '2px';
            customScrollbar.style.right = 'auto';
            customScrollbar.style.left = '0';
        } else {
            scrollbarThumb.style.left = 'auto';
            scrollbarThumb.style.right = '2px';
            customScrollbar.style.left = 'auto';
            customScrollbar.style.right = '0';
        }

        // جعل شريط التمرير قابل للتفاعل
        scrollbarThumb.style.pointerEvents = 'auto';

        // إضافة وظيفة السحب والإفلات للشريط
        let isDragging = false;
        let startY = 0;
        let startScrollY = 0;

        scrollbarThumb.addEventListener('mousedown', function(e) {
            isDragging = true;
            startY = e.clientY;
            startScrollY = window.scrollY;
            document.body.style.userSelect = 'none'; // منع تحديد النص أثناء السحب
        });

        document.addEventListener('mousemove', function(e) {
            if (!isDragging) return;

            const deltaY = e.clientY - startY;
            const scrollRatio = document.documentElement.scrollHeight / window.innerHeight;
            window.scrollTo(0, startScrollY + deltaY * scrollRatio);
        });

        document.addEventListener('mouseup', function() {
            isDragging = false;
            document.body.style.userSelect = ''; // إعادة تمكين تحديد النص
        });
    }

    // تصدير الوظائف للنطاق العام (للتوافق مع الكود القديم)
    window.initCustomScrollbar = initCustomScrollbar;
    window.initKeyboardShortcuts = function() {
        // هذه الدالة موجودة في topbar.js
        if (window.TopbarSystem && window.TopbarSystem.initKeyboardShortcuts) {
            window.TopbarSystem.initKeyboardShortcuts();
        }
    };

})();
