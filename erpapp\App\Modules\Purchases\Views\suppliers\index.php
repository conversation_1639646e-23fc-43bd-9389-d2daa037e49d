<?php
/**
 * صفحة الموردين - مخصصة للجداول فقط
 * تتبع النظام الجديد باستخدام datatable_helper.php
 */

// إعداد الأعمدة
$columns = [
    [
        'field' => 'entity_number',
        'title' => 'الرقم',
        'type' => 'text',
        'width' => '120px',
        'sortable' => true,
        'data_type' => 'number'
    ],
    [
        'field' => 'G_name_ar',
        'title' => 'اسم المورد',
        'type' => 'link',
        'url' => 'purchases/suppliers/{entity_number}',
        'subtitle_field' => 'G_name_en',
        'sortable' => true,
        'data_type' => 'text'
    ],
    [
        'field' => 'S_company_name',
        'title' => 'اسم الشركة',
        'type' => 'text',
        'sortable' => true,
        'data_type' => 'text'
    ],
    [
        'field' => 'group_name',
        'title' => 'المجموعة',
        'type' => 'badge',
        'sortable' => true,
        'data_type' => 'text'
    ],
    [
        'field' => 'G_phone',
        'title' => 'الهاتف',
        'type' => 'text',
        'sortable' => true,
        'data_type' => 'text'
    ],
    [
        'field' => 'G_status',
        'title' => 'الحالة',
        'type' => 'badge',
        'sortable' => true,
        'data_type' => 'text',
        'status_config' => [
            'classes' => [
                'active' => 'success',
                'inactive' => 'secondary',
                'suspended' => 'warning'
            ],
            'texts' => [
                'active' => 'نشط',
                'inactive' => 'غير نشط',
                'suspended' => 'معلق'
            ]
        ]
    ],
    [
        'field' => 'actions',
        'title' => 'الإجراءات',
        'type' => 'actions',
        'width' => '125px',
        'buttons' => [
            [
                'type' => 'link',
                'url' => 'purchases/suppliers/{entity_number}',
                'class' => 'btn-primary',
                'icon' => 'fas fa-eye',
                'title' => 'عرض'
            ],
            [
                'type' => 'link',
                'url' => 'purchases/suppliers/{entity_number}/edit',
                'class' => 'btn-success',
                'icon' => 'fas fa-edit',
                'title' => 'تعديل'
            ],
            [
                'type' => 'button',
                'class' => 'btn-danger',
                'icon' => 'fas fa-trash',
                'title' => 'حذف',
                'onclick' => 'confirmDelete({entity_number})'
            ]
        ]
    ]
];

// الأزرار ستأتي من datatable_helper.php تلقائياً
// لا حاجة لتعريف أزرار محلية

// إعداد الفلاتر
$filters_config = [
    [
        'name' => 'search',
        'type' => 'search',
        'label' => 'البحث في الموردين',
        'placeholder' => 'ابحث بالاسم أو اسم الشركة...',
        'icon' => 'fas fa-search',
        'col_size' => 6,
        'help' => 'البحث في الاسم العربي، الإنجليزي، أو اسم الشركة'
    ],
    [
        'name' => 'status',
        'type' => 'select',
        'label' => 'حالة المورد',
        'placeholder' => 'جميع الحالات',
        'icon' => 'fas fa-check-circle',
        'col_size' => 3,
        'options' => [
            'active' => 'نشط',
            'inactive' => 'غير نشط',
            'suspended' => 'معلق'
        ]
    ],
    [
        'name' => 'group_id',
        'type' => 'select',
        'label' => 'مجموعة الموردين',
        'placeholder' => 'جميع المجموعات',
        'icon' => 'fas fa-folder',
        'col_size' => 3,
        'options' => array_column($supplierGroups ?? [], 'name_ar', 'group_number')
    ]
];

// ملاحظة: الإحصائيات تم نقلها إلى statistics.php
// هذه الصفحة مخصصة للجدول فقط

// إعداد Empty State
$empty_state = [
    'icon' => 'fas fa-truck-loading',
    'message' => 'لا توجد موردين',
    'action' => [
        'url' => 'purchases/suppliers/create',
        'text' => 'إضافة مورد جديد'
    ]
];

// إعداد breadcrumb آمن
$safe_breadcrumb = [
    ['title' => 'المشتريات', 'url' => 'purchases'],
    ['title' => 'الموردين', 'active' => true]
];

// استخدام النظام الجديد - مخصص للجداول فقط
// الأزرار ستأتي من datatable_helper.php تلقائياً
render_datatable_page([
    'title' => $title ?? 'الموردين',
    'module' => 'purchases',
    'entity' => 'suppliers',
    'data' => $suppliers ?? [],
    'columns' => $columns,
    'pagination' => $pagination ?? [],
    'filters' => $filters ?? [],
    'breadcrumb' => $safe_breadcrumb,
    'filters_config' => $filters_config,
    'empty_state' => $empty_state
]);
?>
