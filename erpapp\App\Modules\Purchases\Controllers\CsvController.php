<?php
namespace App\Modules\Purchases\Controllers;

use App\Modules\Purchases\Models\Supplier;
use App\Modules\Purchases\Models\SupplierGroup;
use Exception;

/**
 * CSV Controller - متحكم الاستيراد والتصدير
 */
class CsvController
{
    private $params;
    private $supplierModel;
    private $supplierGroupModel;
    
    /**
     * Constructor
     */
    public function __construct($params = [])
    {
        $this->params = $params;
        $this->supplierModel = new Supplier();
        $this->supplierGroupModel = new SupplierGroup();
        
        // التحقق من تسجيل الدخول
        if (!is_logged_in()) {
            redirect(base_url('login'));
        }
    }
    
    /**
     * تصدير الموردين
     */
    public function exportSuppliers()
    {
        try {
            $export_type = $_GET['type'] ?? 'all';
            $company_id = current_user()['current_company_id'];
            
            // الحصول على البيانات
            $data = $this->getSupplierExportData($export_type, $company_id);
            
            // تكوين الأعمدة
            $columns = $this->getSupplierExportColumns();
            
            // تكوين التصدير
            $config = [
                'title' => 'بيانات الموردين',
                'filename' => 'suppliers_export_' . date('Y-m-d_H-i-s')
            ];
            
            // تصدير البيانات
            $result = export_to_csv($data, $columns, $config);
            
            if ($result['success']) {
                download_csv_file($result['filepath'], $result['filename']);
            } else {
                throw new Exception($result['error']);
            }
            
        } catch (Exception $e) {
            flash('error', 'خطأ في التصدير: ' . $e->getMessage());
            redirect(base_url('purchases/suppliers'));
        }
    }
    
    /**
     * تصدير مجموعات الموردين
     */
    public function exportSupplierGroups()
    {
        try {
            $export_type = $_GET['type'] ?? 'all';
            $company_id = current_user()['current_company_id'];
            
            // الحصول على البيانات
            $data = $this->getSupplierGroupExportData($export_type, $company_id);
            
            // تكوين الأعمدة
            $columns = $this->getSupplierGroupExportColumns();
            
            // تكوين التصدير
            $config = [
                'title' => 'مجموعات الموردين',
                'filename' => 'supplier_groups_export_' . date('Y-m-d_H-i-s')
            ];
            
            // تصدير البيانات
            $result = export_to_csv($data, $columns, $config);
            
            if ($result['success']) {
                download_csv_file($result['filepath'], $result['filename']);
            } else {
                throw new Exception($result['error']);
            }
            
        } catch (Exception $e) {
            flash('error', 'خطأ في التصدير: ' . $e->getMessage());
            redirect(base_url('purchases/supplier-groups'));
        }
    }
    
    /**
     * تنزيل قالب استيراد الموردين
     */
    public function downloadSuppliersTemplate()
    {
        try {
            $columns = $this->getSupplierImportColumns();
            
            $config = [
                'title' => 'قالب استيراد الموردين',
                'filename' => 'suppliers_import_template_' . date('Y-m-d_H-i-s')
            ];
            
            $result = create_csv_template($columns, $config);
            
            if ($result['success']) {
                download_csv_file($result['filepath'], $result['filename']);
            } else {
                throw new Exception($result['error']);
            }
            
        } catch (Exception $e) {
            flash('error', 'خطأ في إنشاء القالب: ' . $e->getMessage());
            redirect(base_url('purchases/suppliers'));
        }
    }
    
    /**
     * تنزيل قالب استيراد مجموعات الموردين
     */
    public function downloadSupplierGroupsTemplate()
    {
        try {
            $columns = $this->getSupplierGroupImportColumns();
            
            $config = [
                'title' => 'قالب استيراد مجموعات الموردين',
                'filename' => 'supplier_groups_import_template_' . date('Y-m-d_H-i-s')
            ];
            
            $result = create_csv_template($columns, $config);
            
            if ($result['success']) {
                download_csv_file($result['filepath'], $result['filename']);
            } else {
                throw new Exception($result['error']);
            }
            
        } catch (Exception $e) {
            flash('error', 'خطأ في إنشاء القالب: ' . $e->getMessage());
            redirect(base_url('purchases/supplier-groups'));
        }
    }
    
    /**
     * رفع ملف استيراد الموردين
     */
    public function uploadSuppliersFile()
    {
        // تعطيل عرض الأخطاء لضمان إرجاع JSON صحيح
        ini_set('display_errors', 0);
        error_reporting(0);
        
        try {
            if (!isset($_FILES['import_file'])) {
                throw new Exception('لم يتم رفع أي ملف');
            }
            
            $file = $_FILES['import_file'];
            
            // التحقق من نوع الملف
            $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            if ($file_extension !== 'csv') {
                throw new Exception('نوع الملف غير مدعوم. يرجى رفع ملف CSV فقط');
            }
            
            // التحقق من حجم الملف (5MB كحد أقصى)
            if ($file['size'] > 5 * 1024 * 1024) {
                throw new Exception('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت');
            }
            
            // نقل الملف إلى مجلد مؤقت
            $upload_dir = sys_get_temp_dir();
            $filename = 'suppliers_import_' . uniqid() . '.csv';
            $filepath = $upload_dir . '/' . $filename;
            
            if (!move_uploaded_file($file['tmp_name'], $filepath)) {
                throw new Exception('فشل في رفع الملف');
            }
            
            // قراءة الملف
            $read_result = read_csv_file($filepath);
            
            if (!$read_result['success']) {
                unlink($filepath);
                throw new Exception('خطأ في قراءة الملف: ' . $read_result['error']);
            }
            
            // حفظ معلومات الملف في الجلسة
            $_SESSION['suppliers_import_file'] = [
                'filepath' => $filepath,
                'filename' => $file['name'],
                'headers' => $read_result['headers'],
                'data' => $read_result['data'],
                'rows_count' => $read_result['rows_count'],
                'uploaded_at' => time()
            ];
            
            // تسجيل للتشخيص
            error_log('Headers being sent: ' . print_r($read_result['headers'], true));

            // إرجاع النتيجة كـ JSON
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => 'تم رفع الملف بنجاح',
                'headers' => $read_result['headers'],
                'rows_count' => $read_result['rows_count'],
                'available_columns' => $this->getSupplierImportColumns()
            ], JSON_UNESCAPED_UNICODE);
            exit;
            
        } catch (Exception $e) {
            // تنظيف أي output buffer
            if (ob_get_level()) {
                ob_clean();
            }
            
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ], JSON_UNESCAPED_UNICODE);
            exit;
        }
    }

    /**
     * معاينة استيراد الموردين
     */
    public function previewSuppliersImport()
    {
        ini_set('display_errors', 0);
        error_reporting(0);

        try {
            if (!isset($_SESSION['suppliers_import_file'])) {
                throw new Exception('لا يوجد ملف مرفوع');
            }

            $import_file = $_SESSION['suppliers_import_file'];
            $input = json_decode(file_get_contents('php://input'), true);
            $mapping = $input['mapping'] ?? [];

            if (empty($mapping)) {
                throw new Exception('يجب تحديد تطابق الأعمدة');
            }

            // الحصول على إعدادات الاستيراد
            $import_columns = $this->getSupplierImportColumns();

            // معاينة البيانات (أول 10 صفوف)
            $preview_data = array_slice($import_file['data'], 0, 10);
            $processed_data = [];
            $errors = [];

            foreach ($preview_data as $index => $row) {
                $processed_row = [];
                $row_errors = [];

                foreach ($import_columns as $column) {
                    $field = $column['field'];
                    $csv_column = $mapping[$field] ?? null;

                    if ($csv_column && isset($row[$csv_column])) {
                        $value = trim($row[$csv_column]);

                        // التحقق من الحقول المطلوبة
                        if ($column['required'] && empty($value)) {
                            $row_errors[] = "الحقل '{$column['title']}' مطلوب";
                        }

                        // التحقق من طول النص
                        if (isset($column['max_length']) && strlen($value) > $column['max_length']) {
                            $row_errors[] = "الحقل '{$column['title']}' طويل جداً (الحد الأقصى {$column['max_length']} حرف)";
                        }

                        // التحقق من البريد الإلكتروني
                        if ($column['data_type'] === 'email' && !empty($value) && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                            $row_errors[] = "البريد الإلكتروني '{$value}' غير صحيح";
                        }

                        $processed_row[$field] = $value;
                    } else {
                        if ($column['required']) {
                            $row_errors[] = "الحقل '{$column['title']}' مطلوب";
                        }
                        $processed_row[$field] = '';
                    }
                }

                $processed_data[] = [
                    'data' => $processed_row,
                    'errors' => $row_errors,
                    'row_number' => $index + 2 // +2 لأن الصف الأول هو الرؤوس
                ];

                $errors = array_merge($errors, $row_errors);
            }

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'preview_data' => $processed_data,
                'total_rows' => $import_file['rows_count'],
                'preview_rows' => count($preview_data),
                'errors' => $errors,
                'has_errors' => !empty($errors)
            ], JSON_UNESCAPED_UNICODE);
            exit;

        } catch (Exception $e) {
            if (ob_get_level()) {
                ob_clean();
            }

            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ], JSON_UNESCAPED_UNICODE);
            exit;
        }
    }

    /**
     * تنفيذ استيراد الموردين
     */
    public function executeSuppliersImport()
    {
        ini_set('display_errors', 0);
        error_reporting(0);

        try {
            if (!isset($_SESSION['suppliers_import_file'])) {
                throw new Exception('لا يوجد ملف مرفوع');
            }

            $import_file = $_SESSION['suppliers_import_file'];
            $input = json_decode(file_get_contents('php://input'), true);
            $mapping = $input['mapping'] ?? [];

            if (empty($mapping)) {
                throw new Exception('يجب تحديد تطابق الأعمدة');
            }

            $company_id = current_user()['current_company_id'];
            $user_id = current_user()['UserID'];

            // الحصول على إعدادات الاستيراد
            $import_columns = $this->getSupplierImportColumns();

            // معالجة البيانات
            $imported_count = 0;
            $errors = [];

            $this->supplierModel->beginTransaction();

            foreach ($import_file['data'] as $index => $row) {
                try {
                    $supplier_data = [
                        'company_id' => $company_id,
                        'created_by' => $user_id
                    ];

                    foreach ($import_columns as $column) {
                        $field = $column['field'];
                        $csv_column = $mapping[$field] ?? null;

                        if ($csv_column && isset($row[$csv_column])) {
                            $value = trim($row[$csv_column]);

                            // التحقق من الحقول المطلوبة
                            if ($column['required'] && empty($value)) {
                                throw new Exception("الحقل '{$column['title']}' مطلوب في الصف " . ($index + 2));
                            }

                            $supplier_data[$field] = $value;
                        } else {
                            if ($column['required']) {
                                throw new Exception("الحقل '{$column['title']}' مطلوب في الصف " . ($index + 2));
                            }
                            $supplier_data[$field] = '';
                        }
                    }

                    // إنشاء المورد
                    $result = $this->supplierModel->create($supplier_data);
                    if ($result) {
                        $imported_count++;
                    }

                } catch (Exception $e) {
                    $errors[] = "الصف " . ($index + 2) . ": " . $e->getMessage();
                }
            }

            $this->supplierModel->commit();

            // تنظيف الملف المؤقت
            if (file_exists($import_file['filepath'])) {
                unlink($import_file['filepath']);
            }
            unset($_SESSION['suppliers_import_file']);

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => 'تم الاستيراد بنجاح',
                'imported_rows' => $imported_count,
                'total_rows' => count($import_file['data']),
                'errors' => $errors
            ], JSON_UNESCAPED_UNICODE);
            exit;

        } catch (Exception $e) {
            $this->supplierModel->rollback();

            if (ob_get_level()) {
                ob_clean();
            }

            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ], JSON_UNESCAPED_UNICODE);
            exit;
        }
    }
    
    /**
     * الحصول على بيانات تصدير الموردين
     */
    private function getSupplierExportData($export_type, $company_id)
    {
        $filters = [];
        if ($export_type === 'filtered' && isset($_SESSION['filters']['suppliers'])) {
            $filters = $_SESSION['filters']['suppliers'];
            unset($filters['per_page'], $filters['current_page'], $filters['limit'], $filters['offset']);
        }
        
        return $this->supplierModel->getByCompany($company_id, $filters);
    }
    
    /**
     * الحصول على بيانات تصدير مجموعات الموردين
     */
    private function getSupplierGroupExportData($export_type, $company_id)
    {
        $filters = [];
        if ($export_type === 'filtered' && isset($_SESSION['filters']['supplier_groups'])) {
            $filters = $_SESSION['filters']['supplier_groups'];
            unset($filters['per_page'], $filters['current_page'], $filters['limit'], $filters['offset']);
        }
        
        return $this->supplierGroupModel->getByCompany($company_id, $filters);
    }
    
    /**
     * الحصول على أعمدة تصدير الموردين
     */
    private function getSupplierExportColumns()
    {
        return [
            ['field' => 'entity_number', 'title' => 'الرقم', 'export' => true],
            ['field' => 'G_name_ar', 'title' => 'اسم المورد', 'export' => true],
            ['field' => 'G_name_en', 'title' => 'الاسم الإنجليزي', 'export' => true],
            ['field' => 'S_company_name', 'title' => 'اسم الشركة', 'export' => true],
            ['field' => 'group_name', 'title' => 'المجموعة', 'export' => true],
            ['field' => 'G_phone', 'title' => 'الهاتف', 'export' => true],
            ['field' => 'G_mobile', 'title' => 'الجوال', 'export' => true],
            ['field' => 'S_email', 'title' => 'البريد الإلكتروني', 'export' => true],
            ['field' => 'G_website', 'title' => 'الموقع الإلكتروني', 'export' => true],
            ['field' => 'S_tax_number', 'title' => 'الرقم الضريبي', 'export' => true],
            ['field' => 'S_commercial_register', 'title' => 'السجل التجاري', 'export' => true],
            ['field' => 'G_status', 'title' => 'الحالة', 'export' => true, 'type' => 'badge',
             'status_config' => [
                 'texts' => ['active' => 'نشط', 'inactive' => 'غير نشط', 'suspended' => 'معلق']
             ]],
            ['field' => 'G_notes', 'title' => 'ملاحظات', 'export' => true]
        ];
    }
    
    /**
     * الحصول على أعمدة تصدير مجموعات الموردين
     */
    private function getSupplierGroupExportColumns()
    {
        return [
            ['field' => 'group_number', 'title' => 'الرقم', 'export' => true],
            ['field' => 'name_ar', 'title' => 'الاسم العربي', 'export' => true],
            ['field' => 'name_en', 'title' => 'الاسم الإنجليزي', 'export' => true],
            ['field' => 'is_default', 'title' => 'افتراضي', 'export' => true, 'type' => 'badge',
             'status_config' => [
                 'texts' => ['1' => 'نعم', '0' => 'لا']
             ]]
        ];
    }
    
    /**
     * الحصول على أعمدة استيراد الموردين
     */
    private function getSupplierImportColumns()
    {
        return [
            ['field' => 'G_name_ar', 'title' => 'اسم المورد', 'required' => true, 'data_type' => 'text', 'max_length' => 255],
            ['field' => 'G_name_en', 'title' => 'الاسم الإنجليزي', 'required' => false, 'data_type' => 'text', 'max_length' => 255],
            ['field' => 'S_company_name', 'title' => 'اسم الشركة', 'required' => false, 'data_type' => 'text', 'max_length' => 255],
            ['field' => 'group_id', 'title' => 'رقم المجموعة', 'required' => false, 'data_type' => 'number'],
            ['field' => 'G_phone', 'title' => 'الهاتف', 'required' => false, 'data_type' => 'text', 'max_length' => 20],
            ['field' => 'G_mobile', 'title' => 'الجوال', 'required' => false, 'data_type' => 'text', 'max_length' => 20],
            ['field' => 'S_email', 'title' => 'البريد الإلكتروني', 'required' => false, 'data_type' => 'email'],
            ['field' => 'G_website', 'title' => 'الموقع الإلكتروني', 'required' => false, 'data_type' => 'url'],
            ['field' => 'G_status', 'title' => 'الحالة', 'required' => false, 'data_type' => 'select',
             'import_options' => ['active' => 'نشط', 'inactive' => 'غير نشط', 'suspended' => 'معلق']],
            ['field' => 'S_tax_number', 'title' => 'الرقم الضريبي', 'required' => false, 'data_type' => 'text', 'max_length' => 50],
            ['field' => 'S_commercial_register', 'title' => 'السجل التجاري', 'required' => false, 'data_type' => 'text', 'max_length' => 50],
            ['field' => 'G_notes', 'title' => 'ملاحظات', 'required' => false, 'data_type' => 'text', 'max_length' => 1000]
        ];
    }
    
    /**
     * الحصول على أعمدة استيراد مجموعات الموردين
     */
    private function getSupplierGroupImportColumns()
    {
        return [
            ['field' => 'name_ar', 'title' => 'الاسم العربي', 'required' => true, 'data_type' => 'text', 'max_length' => 255],
            ['field' => 'name_en', 'title' => 'الاسم الإنجليزي', 'required' => false, 'data_type' => 'text', 'max_length' => 255],
            ['field' => 'is_default', 'title' => 'افتراضي', 'required' => false, 'data_type' => 'select',
             'import_options' => ['0' => 'لا', '1' => 'نعم']]
        ];
    }

    /**
     * رفع ملف استيراد مجموعات الموردين
     */
    public function uploadSupplierGroupsFile()
    {
        // تعطيل عرض الأخطاء لضمان إرجاع JSON صحيح
        ini_set('display_errors', 0);
        error_reporting(0);

        try {
            if (!isset($_FILES['import_file'])) {
                throw new Exception('لم يتم رفع أي ملف');
            }

            $file = $_FILES['import_file'];

            // التحقق من نوع الملف
            $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            if ($file_extension !== 'csv') {
                throw new Exception('نوع الملف غير مدعوم. يرجى رفع ملف CSV فقط');
            }

            // التحقق من حجم الملف (5MB كحد أقصى)
            if ($file['size'] > 5 * 1024 * 1024) {
                throw new Exception('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت');
            }

            // نقل الملف إلى مجلد مؤقت
            $upload_dir = sys_get_temp_dir();
            $filename = 'supplier_groups_import_' . uniqid() . '.csv';
            $filepath = $upload_dir . '/' . $filename;

            if (!move_uploaded_file($file['tmp_name'], $filepath)) {
                throw new Exception('فشل في رفع الملف');
            }

            // قراءة الملف
            $read_result = read_csv_file($filepath);

            if (!$read_result['success']) {
                unlink($filepath);
                throw new Exception('خطأ في قراءة الملف: ' . $read_result['error']);
            }

            // حفظ معلومات الملف في الجلسة
            $_SESSION['supplier_groups_import_file'] = [
                'filepath' => $filepath,
                'filename' => $file['name'],
                'headers' => $read_result['headers'],
                'data' => $read_result['data'],
                'rows_count' => $read_result['rows_count'],
                'uploaded_at' => time()
            ];

            // إرجاع النتيجة كـ JSON
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => 'تم رفع الملف بنجاح',
                'headers' => $read_result['headers'],
                'rows_count' => $read_result['rows_count'],
                'available_columns' => $this->getSupplierGroupImportColumns()
            ], JSON_UNESCAPED_UNICODE);
            exit;

        } catch (Exception $e) {
            // تنظيف أي output buffer
            if (ob_get_level()) {
                ob_clean();
            }

            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ], JSON_UNESCAPED_UNICODE);
            exit;
        }
    }

    /**
     * معاينة استيراد مجموعات الموردين
     */
    public function previewSupplierGroupsImport()
    {
        ini_set('display_errors', 0);
        error_reporting(0);

        try {
            if (!isset($_SESSION['supplier_groups_import_file'])) {
                throw new Exception('لا يوجد ملف مرفوع');
            }

            $import_file = $_SESSION['supplier_groups_import_file'];
            $input = json_decode(file_get_contents('php://input'), true);
            $mapping = $input['mapping'] ?? [];

            if (empty($mapping)) {
                throw new Exception('يجب تحديد تطابق الأعمدة');
            }

            // الحصول على إعدادات الاستيراد
            $import_columns = $this->getSupplierGroupImportColumns();

            // معاينة البيانات (أول 10 صفوف)
            $preview_data = array_slice($import_file['data'], 0, 10);
            $processed_data = [];
            $errors = [];

            foreach ($preview_data as $index => $row) {
                $processed_row = [];
                $row_errors = [];

                foreach ($import_columns as $column) {
                    $field = $column['field'];
                    $csv_column = $mapping[$field] ?? null;

                    if ($csv_column && isset($row[$csv_column])) {
                        $value = trim($row[$csv_column]);

                        // التحقق من الحقول المطلوبة
                        if ($column['required'] && empty($value)) {
                            $row_errors[] = "الحقل '{$column['title']}' مطلوب";
                        }

                        // التحقق من طول النص
                        if (isset($column['max_length']) && strlen($value) > $column['max_length']) {
                            $row_errors[] = "الحقل '{$column['title']}' طويل جداً (الحد الأقصى {$column['max_length']} حرف)";
                        }

                        $processed_row[$field] = $value;
                    } else {
                        if ($column['required']) {
                            $row_errors[] = "الحقل '{$column['title']}' مطلوب";
                        }
                        $processed_row[$field] = '';
                    }
                }

                $processed_data[] = [
                    'data' => $processed_row,
                    'errors' => $row_errors,
                    'row_number' => $index + 2 // +2 لأن الصف الأول هو الرؤوس
                ];

                $errors = array_merge($errors, $row_errors);
            }

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'preview_data' => $processed_data,
                'total_rows' => $import_file['rows_count'],
                'preview_rows' => count($preview_data),
                'errors' => $errors,
                'has_errors' => !empty($errors)
            ], JSON_UNESCAPED_UNICODE);
            exit;

        } catch (Exception $e) {
            if (ob_get_level()) {
                ob_clean();
            }

            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ], JSON_UNESCAPED_UNICODE);
            exit;
        }
    }

    /**
     * تنفيذ استيراد مجموعات الموردين
     */
    public function executeSupplierGroupsImport()
    {
        ini_set('display_errors', 0);
        error_reporting(0);

        try {
            if (!isset($_SESSION['supplier_groups_import_file'])) {
                throw new Exception('لا يوجد ملف مرفوع');
            }

            $import_file = $_SESSION['supplier_groups_import_file'];
            $input = json_decode(file_get_contents('php://input'), true);
            $mapping = $input['mapping'] ?? [];

            if (empty($mapping)) {
                throw new Exception('يجب تحديد تطابق الأعمدة');
            }

            $company_id = current_user()['current_company_id'];
            $user_id = current_user()['UserID'];

            // الحصول على إعدادات الاستيراد
            $import_columns = $this->getSupplierGroupImportColumns();

            // معالجة البيانات
            $imported_count = 0;
            $errors = [];

            $this->supplierGroupModel->beginTransaction();

            foreach ($import_file['data'] as $index => $row) {
                try {
                    $group_data = [
                        'company_id' => $company_id,
                        'created_by' => $user_id
                    ];

                    foreach ($import_columns as $column) {
                        $field = $column['field'];
                        $csv_column = $mapping[$field] ?? null;

                        if ($csv_column && isset($row[$csv_column])) {
                            $value = trim($row[$csv_column]);

                            // التحقق من الحقول المطلوبة
                            if ($column['required'] && empty($value)) {
                                throw new Exception("الحقل '{$column['title']}' مطلوب في الصف " . ($index + 2));
                            }

                            $group_data[$field] = $value;
                        } else {
                            if ($column['required']) {
                                throw new Exception("الحقل '{$column['title']}' مطلوب في الصف " . ($index + 2));
                            }
                            $group_data[$field] = '';
                        }
                    }

                    // إنشاء المجموعة
                    $result = $this->supplierGroupModel->create($group_data);
                    if ($result) {
                        $imported_count++;
                    }

                } catch (Exception $e) {
                    $errors[] = "الصف " . ($index + 2) . ": " . $e->getMessage();
                }
            }

            $this->supplierGroupModel->commit();

            // تنظيف الملف المؤقت
            if (file_exists($import_file['filepath'])) {
                unlink($import_file['filepath']);
            }
            unset($_SESSION['supplier_groups_import_file']);

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => 'تم الاستيراد بنجاح',
                'imported_rows' => $imported_count,
                'total_rows' => count($import_file['data']),
                'errors' => $errors
            ], JSON_UNESCAPED_UNICODE);
            exit;

        } catch (Exception $e) {
            $this->supplierGroupModel->rollback();

            if (ob_get_level()) {
                ob_clean();
            }

            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ], JSON_UNESCAPED_UNICODE);
            exit;
        }
    }
}
?>
