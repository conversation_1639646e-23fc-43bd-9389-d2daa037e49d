<?php
// تضمين القالب المشترك للحصول على $form_tabs
include __DIR__ . '/form_template.php';
// إعداد الإجراءات للعرض فقط
$actions = [
    [
        'type' => 'secondary',
        'url' => 'purchases/suppliers',
        'icon' => 'fas fa-arrow-left',
        'text' => 'العودة للقائمة'
    ],
    [
        'type' => 'primary',
        'url' => 'purchases/suppliers/' . ($supplier['entity_number'] ?? '') . '/edit',
        'icon' => 'fas fa-edit',
        'text' => 'تعديل المورد'
    ],
    [
        'type' => 'danger',
        'url' => 'purchases/suppliers/' . ($supplier['entity_number'] ?? '') . '/delete',
        'icon' => 'fas fa-trash',
        'text' => 'حذف المورد',
        'onclick' => 'return confirm("هل أنت متأكد من حذف هذا المورد؟")'
    ]
];

// إعداد breadcrumb
$safe_breadcrumb = [
    ['title' => 'المشتريات', 'url' => 'purchases'],
    ['title' => 'الموردين', 'url' => 'purchases/suppliers'],
    ['title' => 'عرض المورد', 'active' => true]
];

// استخدام النظام الموحد في وضع القراءة فقط
render_form_page([
    'title' => $title ?? 'عرض المورد',
    'module' => 'purchases',
    'entity' => 'suppliers',
    'breadcrumb' => $safe_breadcrumb,
    'actions' => $actions,
    'form_data' => $supplier ?? [], // بيانات المورد الحالية
    'readonly' => true, // وضع القراءة فقط
    // النموذج بالتبويبات الديناميكية
    'form' => [
        'action' => '#', // لا يوجد action في وضع العرض
        'method' => 'GET',
        'id' => 'supplierForm',
        'tabs' => $form_tabs
    ]
]);
?>
