<?php
namespace App\Modules\Purchases\Controllers;

use App\Modules\Purchases\Models\Supplier;
use App\Modules\Purchases\Models\SupplierGroup;
use Exception;

/**
 * ImportExportController - متحكم الاستيراد والتصدير لوحدة المشتريات
 */
class ImportExportController
{
    /**
     * Route parameters
     */
    protected $params = [];

    /**
     * Supplier model
     */
    protected $supplierModel;

    /**
     * SupplierGroup model
     */
    protected $supplierGroupModel;

    /**
     * Constructor
     */
    public function __construct($params = [])
    {
        $this->params = $params;
        $this->supplierModel = new Supplier();
        $this->supplierGroupModel = new SupplierGroup();

        // تحميل مكتبة PhpSpreadsheet إذا لم تكن محملة
        $this->loadPhpSpreadsheet();

        // التحقق من تسجيل الدخول
        if (!is_logged_in()) {
            redirect(base_url('login'));
        }

        // التحقق من وجود شركة حالية
        $user = current_user();
        if (!$user || !$user['current_company_id']) {
            flash('error', 'يجب تحديد شركة حالية للوصول إلى الاستيراد والتصدير', 'warning');
            redirect(base_url('companies'));
        }
    }

    /**
     * تصدير الموردين
     */
    public function exportSuppliers()
    {
        try {
            $export_type = $_POST['export_type'] ?? 'current';
            $include_filters = isset($_POST['include_filters']);

            // الحصول على البيانات
            $data = $this->getSupplierExportData($export_type);
            
            if (empty($data)) {
                throw new Exception('لا توجد بيانات للتصدير');
            }

            // إعداد الأعمدة
            $columns = $this->getSupplierExportColumns();

            // إعداد التصدير
            $config = [
                'title' => 'قائمة الموردين',
                'filename' => 'suppliers_export_' . date('Y-m-d_H-i-s'),
                'include_filters' => $include_filters,
                'filters' => $include_filters ? ($_SESSION['filters']['suppliers'] ?? []) : []
            ];

            // تصدير البيانات (ذكي - Excel أو CSV)
            $result = smart_export_data($data, $columns, $config);

            if ($result['success']) {
                // تنزيل الملف
                download_file($result['filepath'], $result['filename']);
                exit;
            } else {
                throw new Exception($result['error']);
            }

        } catch (Exception $e) {
            flash('error', 'خطأ في التصدير: ' . $e->getMessage());
            redirect(base_url('purchases/suppliers'));
        }
    }

    /**
     * تنزيل قالب استيراد الموردين
     */
    public function downloadSuppliersTemplate()
    {
        try {
            // إعداد الأعمدة
            $columns = $this->getSupplierImportColumns();

            // إنشاء القالب
            $config = [
                'title' => 'قالب استيراد الموردين',
                'filename' => 'suppliers_import_template_' . date('Y-m-d_H-i-s')
            ];

            $result = create_import_template($columns, $config);

            if ($result['success']) {
                download_file($result['filepath'], $result['filename']);
                exit;
            } else {
                throw new Exception($result['error']);
            }

        } catch (Exception $e) {
            flash('error', 'خطأ في إنشاء القالب: ' . $e->getMessage());
            redirect(base_url('purchases/suppliers'));
        }
    }

    /**
     * رفع ملف استيراد الموردين
     */
    public function uploadSuppliersFile()
    {
        try {
            if (!isset($_FILES['import_file'])) {
                throw new Exception('لم يتم رفع أي ملف');
            }

            $file = $_FILES['import_file'];

            // التحقق من نوع الملف
            $allowed_types = ['xlsx', 'xls'];
            $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            
            if (!in_array($file_extension, $allowed_types)) {
                throw new Exception('نوع الملف غير مدعوم. يرجى رفع ملف Excel (.xlsx أو .xls)');
            }

            // التحقق من حجم الملف (5MB كحد أقصى)
            if ($file['size'] > 5 * 1024 * 1024) {
                throw new Exception('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت');
            }

            // نقل الملف إلى مجلد مؤقت
            $upload_dir = sys_get_temp_dir();
            $filename = 'suppliers_import_' . uniqid() . '.' . $file_extension;
            $filepath = $upload_dir . '/' . $filename;

            if (!move_uploaded_file($file['tmp_name'], $filepath)) {
                throw new Exception('فشل في رفع الملف');
            }

            // قراءة الملف
            $read_result = read_excel_file($filepath);
            
            if (!$read_result['success']) {
                unlink($filepath); // حذف الملف
                throw new Exception('خطأ في قراءة الملف: ' . $read_result['error']);
            }

            // حفظ معلومات الملف في الجلسة
            $_SESSION['suppliers_import_file'] = [
                'filepath' => $filepath,
                'filename' => $file['name'],
                'headers' => $read_result['headers'],
                'data' => $read_result['data'],
                'rows_count' => $read_result['rows_count'],
                'uploaded_at' => time()
            ];

            // إرجاع النتيجة كـ JSON
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => 'تم رفع الملف بنجاح',
                'headers' => $read_result['headers'],
                'rows_count' => $read_result['rows_count']
            ]);
            exit;

        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
            exit;
        }
    }

    /**
     * معاينة استيراد الموردين
     */
    public function previewSuppliersImport()
    {
        try {
            if (!isset($_SESSION['suppliers_import_file'])) {
                throw new Exception('لا يوجد ملف مرفوع');
            }

            $import_file = $_SESSION['suppliers_import_file'];
            $mapping = $_POST['mapping'] ?? [];

            if (empty($mapping)) {
                throw new Exception('يجب تحديد تطابق الأعمدة');
            }

            // الحصول على إعدادات الاستيراد
            $import_columns = $this->getSupplierImportColumns();

            // معالجة التطابق
            $mapping_result = process_column_mapping(
                $import_file['headers'],
                $import_columns,
                $mapping
            );

            if (!$mapping_result['success']) {
                throw new Exception(implode('<br>', $mapping_result['errors']));
            }

            // معاينة البيانات (أول 10 صفوف)
            $preview_data = array_slice($import_file['data'], 0, 10);
            
            $validation_result = import_data_with_validation(
                $preview_data,
                $mapping,
                $import_columns,
                [$this, 'validateSupplierData']
            );

            // إرجاع النتيجة
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'preview_data' => $validation_result['data'],
                'total_rows' => $import_file['rows_count'],
                'preview_rows' => count($preview_data),
                'errors' => $validation_result['errors']
            ]);
            exit;

        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
            exit;
        }
    }

    /**
     * تنفيذ استيراد الموردين
     */
    public function executeSuppliersImport()
    {
        try {
            if (!isset($_SESSION['suppliers_import_file'])) {
                throw new Exception('لا يوجد ملف مرفوع');
            }

            $import_file = $_SESSION['suppliers_import_file'];
            $mapping = $_POST['mapping'] ?? [];

            if (empty($mapping)) {
                throw new Exception('يجب تحديد تطابق الأعمدة');
            }

            // الحصول على إعدادات الاستيراد
            $import_columns = $this->getSupplierImportColumns();

            // استيراد البيانات مع التحقق
            $validation_result = import_data_with_validation(
                $import_file['data'],
                $mapping,
                $import_columns,
                [$this, 'validateSupplierData']
            );

            if (!$validation_result['success']) {
                throw new Exception('فشل في التحقق من البيانات:<br>' . implode('<br>', $validation_result['errors']));
            }

            // حفظ البيانات في قاعدة البيانات
            $save_result = $this->saveSuppliers($validation_result['data']);

            if (!$save_result['success']) {
                throw new Exception($save_result['error']);
            }

            // تنظيف الملف المؤقت
            if (file_exists($import_file['filepath'])) {
                unlink($import_file['filepath']);
            }
            unset($_SESSION['suppliers_import_file']);

            // إرجاع النتيجة
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => 'تم الاستيراد بنجاح',
                'imported_rows' => $save_result['imported_rows'],
                'total_rows' => $validation_result['total_rows']
            ]);
            exit;

        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
            exit;
        }
    }

    /**
     * الحصول على بيانات تصدير الموردين
     */
    private function getSupplierExportData($export_type)
    {
        $company_id = current_user()['current_company_id'];

        $filters = [];
        if ($export_type === 'filtered' && isset($_SESSION['filters']['suppliers'])) {
            $filters = $_SESSION['filters']['suppliers'];
            unset($filters['per_page'], $filters['current_page'], $filters['limit'], $filters['offset']);
        }

        return $this->supplierModel->getByCompany($company_id, $filters);
    }

    /**
     * الحصول على أعمدة تصدير الموردين
     */
    private function getSupplierExportColumns()
    {
        return [
            ['field' => 'entity_number', 'title' => 'الرقم', 'export' => true],
            ['field' => 'G_name_ar', 'title' => 'اسم المورد', 'export' => true],
            ['field' => 'G_name_en', 'title' => 'الاسم الإنجليزي', 'export' => true],
            ['field' => 'S_company_name', 'title' => 'اسم الشركة', 'export' => true],
            ['field' => 'group_name', 'title' => 'المجموعة', 'export' => true],
            ['field' => 'G_phone', 'title' => 'الهاتف', 'export' => true],
            ['field' => 'G_mobile', 'title' => 'الجوال', 'export' => true],
            ['field' => 'S_email', 'title' => 'البريد الإلكتروني', 'export' => true],
            ['field' => 'G_website', 'title' => 'الموقع الإلكتروني', 'export' => true],
            ['field' => 'S_tax_number', 'title' => 'الرقم الضريبي', 'export' => true],
            ['field' => 'S_commercial_register', 'title' => 'السجل التجاري', 'export' => true],
            ['field' => 'G_status', 'title' => 'الحالة', 'export' => true, 'type' => 'badge',
             'status_config' => [
                 'texts' => ['active' => 'نشط', 'inactive' => 'غير نشط', 'suspended' => 'معلق']
             ]],
            ['field' => 'G_notes', 'title' => 'ملاحظات', 'export' => true]
        ];
    }

    /**
     * الحصول على أعمدة استيراد الموردين
     */
    private function getSupplierImportColumns()
    {
        return [
            ['field' => 'G_name_ar', 'title' => 'اسم المورد *', 'required' => true, 'data_type' => 'text', 'max_length' => 255],
            ['field' => 'G_name_en', 'title' => 'الاسم الإنجليزي', 'required' => false, 'data_type' => 'text', 'max_length' => 255],
            ['field' => 'S_company_name', 'title' => 'اسم الشركة', 'required' => false, 'data_type' => 'text', 'max_length' => 255],
            ['field' => 'G_phone', 'title' => 'الهاتف', 'required' => false, 'data_type' => 'text', 'max_length' => 20],
            ['field' => 'G_mobile', 'title' => 'الجوال', 'required' => false, 'data_type' => 'text', 'max_length' => 20],
            ['field' => 'S_email', 'title' => 'البريد الإلكتروني', 'required' => false, 'data_type' => 'email'],
            ['field' => 'G_website', 'title' => 'الموقع الإلكتروني', 'required' => false, 'data_type' => 'url'],
            ['field' => 'G_status', 'title' => 'الحالة', 'required' => false, 'data_type' => 'select',
             'import_options' => ['active' => 'نشط', 'inactive' => 'غير نشط', 'suspended' => 'معلق']],
            ['field' => 'S_tax_number', 'title' => 'الرقم الضريبي', 'required' => false, 'data_type' => 'text', 'max_length' => 50],
            ['field' => 'S_commercial_register', 'title' => 'السجل التجاري', 'required' => false, 'data_type' => 'text', 'max_length' => 50],
            ['field' => 'G_notes', 'title' => 'ملاحظات', 'required' => false, 'data_type' => 'text', 'max_length' => 1000]
        ];
    }

    /**
     * حفظ الموردين المستوردين
     */
    private function saveSuppliers($suppliers)
    {
        try {
            $company_id = current_user()['current_company_id'];
            $user_id = current_user()['UserID'];

            $this->supplierModel->beginTransaction();

            $imported_count = 0;

            foreach ($suppliers as $supplier_data) {
                // إضافة البيانات المطلوبة
                $supplier_data['company_id'] = $company_id;
                $supplier_data['created_by'] = $user_id;
                $supplier_data['G_status'] = $supplier_data['G_status'] ?? 'active';

                // إنشاء المورد
                $result = $this->supplierModel->create($supplier_data);
                if ($result) {
                    $imported_count++;
                }
            }

            $this->supplierModel->commit();

            return [
                'success' => true,
                'imported_rows' => $imported_count
            ];

        } catch (Exception $e) {
            $this->supplierModel->rollback();
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * التحقق من صحة بيانات المورد
     */
    public function validateSupplierData($data, $row_number)
    {
        $errors = [];

        // التحقق من عدم تكرار الاسم
        if (!empty($data['G_name_ar'])) {
            $company_id = current_user()['current_company_id'];

            // يمكن إضافة فحص التكرار هنا إذا لزم الأمر
            // $existing = $this->supplierModel->checkDuplicate($data['G_name_ar'], $company_id);
            // if ($existing) {
            //     $errors[] = "اسم المورد '{$data['G_name_ar']}' موجود مسبقاً في الصف {$row_number}";
            // }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * تحميل مكتبة PhpSpreadsheet
     */
    private function loadPhpSpreadsheet()
    {
        if (!class_exists('PhpOffice\PhpSpreadsheet\Spreadsheet')) {
            // استخدام نفس المسار الذي يعمل في loader.php
            $autoload_path = dirname(BASE_PATH) . '../vendor/autoload.php';
            if (file_exists($autoload_path)) {
                require_once $autoload_path;
            }
        }
    }
}
