<?php
/**
 * صفحة مجموعات الموردين باستخدام النظام الموحد
 * تم تحويلها من النظام القديم إلى النظام الجديد
 */

// إعداد الأعمدة
$columns = [
    [
        'field' => 'group_number',
        'title' => 'الرقم',
        'type' => 'text',
        'width' => '120px',
        'sortable' => true,
        'data_type' => 'number'
    ],
    [
        'field' => 'name_ar',
        'title' => 'اسم المجموعة',
        'type' => 'link',
        'url' => 'purchases/supplier-groups/{group_number}',
        'subtitle_field' => 'name_en',
        'sortable' => true,
        'data_type' => 'text'
    ],
    [
        'field' => 'is_default',
        'title' => 'المجموعة الافتراضية',
        'type' => 'badge',
        'sortable' => true,
        'data_type' => 'text',
        'status_config' => [
            'classes' => [
                '1' => 'success',
                '0' => 'secondary'
            ],
            'texts' => [
                '1' => 'افتراضي',
                '0' => '-'
            ]
        ]
    ],
    [
        'field' => 'created_at',
        'title' => 'تاريخ الإنشاء',
        'type' => 'text',
        'sortable' => true,
        'data_type' => 'date'
    ],
    [
        'field' => 'actions',
        'title' => 'الإجراءات',
        'type' => 'actions',
        'width' => '125px',
        'buttons' => [
            [
                'type' => 'link',
                'url' => 'purchases/supplier-groups/{group_number}',
                'class' => 'btn-primary',
                'icon' => 'mdi mdi-eye',
                'title' => 'عرض'
            ],
            [
                'type' => 'link',
                'url' => 'purchases/supplier-groups/{group_number}/edit',
                'class' => 'btn-success',
                'icon' => 'mdi mdi-pencil',
                'title' => 'تعديل'
            ],
            [
                'type' => 'button',
                'class' => 'btn-danger',
                'icon' => 'mdi mdi-delete',
                'title' => 'حذف',
                'onclick' => 'confirmDelete({group_number})'
            ]
        ]
    ]
];

// إعداد الإجراءات (بدون أزرار CSV)
$actions = [
    [
        'type' => 'primary',
        'url' => 'purchases/supplier-groups/create',
        'icon' => 'mdi mdi-plus-circle',
        'text' => 'إضافة مجموعة جديدة'
    ]
];

// إعداد أزرار CSV للجدول (بدون قالب الاستيراد)
$csv_actions = [
    [
        'type' => 'csv_export',
        'url' => 'purchases/supplier-groups/export',
        'icon' => 'fas fa-download',
        'text' => 'تصدير CSV'
    ],
    [
        'type' => 'csv_import',
        'icon' => 'fas fa-upload',
        'text' => 'استيراد CSV'
    ]
];

// إعداد الفلاتر
$filters_config = [
    [
        'name' => 'search',
        'type' => 'search',
        'label' => 'البحث في المجموعات',
        'placeholder' => 'ابحث بالاسم العربي أو الإنجليزي...',
        'icon' => 'mdi mdi-magnify',
        'col_size' => 8,
        'help' => 'البحث في الاسم العربي أو الإنجليزي للمجموعة'
    ],
    [
        'name' => 'is_default',
        'type' => 'select',
        'label' => 'نوع المجموعة',
        'placeholder' => 'جميع المجموعات',
        'icon' => 'mdi mdi-star',
        'col_size' => 4,
        'options' => [
            '1' => 'المجموعات الافتراضية',
            '0' => 'المجموعات العادية'
        ]
    ]
];

// إعداد الإحصائيات
$stats_cards = [
    [
        'title' => 'إجمالي المجموعات',
        'value' => $stats['total_groups'] ?? 0,
        'icon' => 'mdi mdi-folder-multiple',
        'color' => 'muted'
    ],
    [
        'title' => 'المجموعات الافتراضية',
        'value' => $stats['default_groups'] ?? 0,
        'icon' => 'mdi mdi-star',
        'color' => 'warning'
    ]
];

// إعداد Empty State
$empty_state = [
    'icon' => 'mdi mdi-folder-open-outline',
    'message' => 'لا توجد مجموعات موردين',
    'action' => [
        'url' => 'purchases/supplier-groups/create',
        'text' => 'إضافة مجموعة جديدة'
    ]
];

// إعداد breadcrumb آمن
$safe_breadcrumb = [
    ['title' => 'المشتريات', 'url' => 'purchases'],
    ['title' => 'مجموعات الموردين', 'active' => true]
];

// تحويل البيانات لتتوافق مع النظام الجديد
$processedData = [];
if (!empty($supplierGroups)) {
    foreach ($supplierGroups as $group) {
        $processedData[] = [
            'group_number' => $group['group_number'],
            'name_ar' => $group['name_ar'],
            'name_en' => $group['name_en'] ?: '',
            'is_default' => $group['is_default'] ?? '0',
            'created_at' => isset($group['created_at']) ? date('Y-m-d', strtotime($group['created_at'])) : '-'
        ];
    }
}

// استخدام النظام الموحد
render_datatable_page([
    'title' => $title ?? 'مجموعات الموردين',
    'module' => 'purchases',
    'entity' => 'supplier-groups',
    'data' => $processedData,
    'columns' => $columns,
    'stats' => $stats_cards,
    'pagination' => $pagination ?? [],
    'filters' => $filters ?? [],
    'breadcrumb' => $safe_breadcrumb,
    'actions' => $actions,
    'csv_actions' => $csv_actions,
    'filters_config' => $filters_config,
    'empty_state' => $empty_state
]);

// عرض نافذة استيراد CSV
render_csv_import_modal('purchases', 'supplier-groups');
?>
