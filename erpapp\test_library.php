<?php
/**
 * اختبار سريع للمكتبة
 */

define('BASE_PATH', __DIR__);

echo "<h2>اختبار مكتبة PhpSpreadsheet</h2>";

// تحميل autoloader
$composer_autoloader = dirname(BASE_PATH) . '../vendor/autoload.php';
echo "<p>مسار autoloader: " . $composer_autoloader . "</p>";

if (file_exists($composer_autoloader)) {
    echo "<p style='color: green;'>✓ ملف autoloader موجود</p>";
    
    require_once $composer_autoloader;
    
    if (class_exists('PhpOffice\PhpSpreadsheet\Spreadsheet')) {
        echo "<p style='color: green;'>✓ مكتبة PhpSpreadsheet محملة بنجاح!</p>";
        
        // اختبار إنشاء ملف Excel بسيط
        try {
            require_once 'App/Helpers/import_export_helper.php';
            
            $test_data = [
                ['name' => 'اختبار 1', 'value' => '100'],
                ['name' => 'اختبار 2', 'value' => '200']
            ];
            
            $test_columns = [
                ['field' => 'name', 'title' => 'الاسم', 'export' => true],
                ['field' => 'value', 'title' => 'القيمة', 'export' => true]
            ];
            
            $result = export_data_to_excel($test_data, $test_columns, ['title' => 'اختبار', 'filename' => 'test']);
            
            if ($result['success']) {
                echo "<p style='color: green;'>✓ تم إنشاء ملف Excel بنجاح!</p>";
                // حذف الملف المؤقت
                if (file_exists($result['filepath'])) {
                    unlink($result['filepath']);
                }
            } else {
                echo "<p style='color: red;'>✗ فشل في إنشاء ملف Excel: " . $result['error'] . "</p>";
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ خطأ في الاختبار: " . $e->getMessage() . "</p>";
        }
        
    } else {
        echo "<p style='color: red;'>✗ مكتبة PhpSpreadsheet غير محملة</p>";
    }
    
} else {
    echo "<p style='color: red;'>✗ ملف autoloader غير موجود</p>";
}

echo "<p><strong>الآن يمكنك استخدام الاستيراد والتصدير!</strong></p>";
?>
