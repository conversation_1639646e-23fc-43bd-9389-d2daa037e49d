<?php
/**
 * ملف تشخيص مشاكل استيراد CSV
 */

require_once 'config/config.php';
require_once 'App/Helpers/csv_helper.php';

echo "<h2>🔍 تشخيص مشاكل استيراد CSV</h2>";

// اختبار قراءة ملف CSV تجريبي
echo "<h3>1. اختبار قراءة ملف CSV</h3>";

// إنشاء ملف CSV تجريبي
$test_csv_content = "اسم المورد,اسم الشركة,الهاتف,البريد الإلكتروني\n";
$test_csv_content .= "شركة المثال,شركة المثال المحدودة,0112345678,<EMAIL>\n";
$test_csv_content .= "مورد تجريبي,مؤسسة تجريبية,0501234567,<EMAIL>\n";

$test_file = sys_get_temp_dir() . '/test_suppliers.csv';
file_put_contents($test_file, "\xEF\xBB\xBF" . $test_csv_content); // إضافة BOM

echo "<p><strong>محتوى الملف التجريبي:</strong></p>";
echo "<pre>" . htmlspecialchars($test_csv_content) . "</pre>";

// قراءة الملف
$result = read_csv_file($test_file);

echo "<p><strong>نتيجة القراءة:</strong></p>";
echo "<pre>";
print_r($result);
echo "</pre>";

if ($result['success']) {
    echo "<p>✅ <strong>قراءة الملف نجحت!</strong></p>";
    
    echo "<h4>رؤوس الأعمدة:</h4>";
    echo "<ul>";
    foreach ($result['headers'] as $index => $header) {
        echo "<li>العمود {$index}: <strong>" . htmlspecialchars($header) . "</strong></li>";
    }
    echo "</ul>";
    
    echo "<h4>البيانات:</h4>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr>";
    foreach ($result['headers'] as $header) {
        echo "<th>" . htmlspecialchars($header) . "</th>";
    }
    echo "</tr>";
    
    foreach ($result['data'] as $row) {
        echo "<tr>";
        foreach ($result['headers'] as $header) {
            echo "<td>" . htmlspecialchars($row[$header] ?? '') . "</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
    
} else {
    echo "<p>❌ <strong>فشل في قراءة الملف:</strong> " . $result['error'] . "</p>";
}

// اختبار إعدادات الاستيراد
echo "<h3>2. اختبار إعدادات الاستيراد</h3>";

// محاكاة CsvController
class TestCsvController {
    private function getSupplierImportColumns()
    {
        return [
            ['field' => 'G_name_ar', 'title' => 'اسم المورد', 'required' => true, 'data_type' => 'text', 'max_length' => 255],
            ['field' => 'G_name_en', 'title' => 'الاسم الإنجليزي', 'required' => false, 'data_type' => 'text', 'max_length' => 255],
            ['field' => 'S_company_name', 'title' => 'اسم الشركة', 'required' => false, 'data_type' => 'text', 'max_length' => 255],
            ['field' => 'group_id', 'title' => 'رقم المجموعة', 'required' => false, 'data_type' => 'number'],
            ['field' => 'G_phone', 'title' => 'الهاتف', 'required' => false, 'data_type' => 'text', 'max_length' => 20],
            ['field' => 'G_mobile', 'title' => 'الجوال', 'required' => false, 'data_type' => 'text', 'max_length' => 20],
            ['field' => 'S_email', 'title' => 'البريد الإلكتروني', 'required' => false, 'data_type' => 'email'],
            ['field' => 'G_website', 'title' => 'الموقع الإلكتروني', 'required' => false, 'data_type' => 'url'],
            ['field' => 'G_status', 'title' => 'الحالة', 'required' => false, 'data_type' => 'select',
             'import_options' => ['active' => 'نشط', 'inactive' => 'غير نشط', 'suspended' => 'معلق']],
            ['field' => 'S_tax_number', 'title' => 'الرقم الضريبي', 'required' => false, 'data_type' => 'text', 'max_length' => 50],
            ['field' => 'S_commercial_register', 'title' => 'السجل التجاري', 'required' => false, 'data_type' => 'text', 'max_length' => 50],
            ['field' => 'G_notes', 'title' => 'ملاحظات', 'required' => false, 'data_type' => 'text', 'max_length' => 1000]
        ];
    }
    
    public function getColumns() {
        return $this->getSupplierImportColumns();
    }
}

$testController = new TestCsvController();
$import_columns = $testController->getColumns();

echo "<p><strong>أعمدة الاستيراد المتاحة:</strong></p>";
echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>الحقل</th><th>العنوان</th><th>مطلوب</th><th>نوع البيانات</th></tr>";
foreach ($import_columns as $column) {
    echo "<tr>";
    echo "<td>" . htmlspecialchars($column['field']) . "</td>";
    echo "<td>" . htmlspecialchars($column['title']) . "</td>";
    echo "<td>" . ($column['required'] ? 'نعم' : 'لا') . "</td>";
    echo "<td>" . htmlspecialchars($column['data_type']) . "</td>";
    echo "</tr>";
}
echo "</table>";

// اختبار تطابق الأعمدة
echo "<h3>3. اختبار تطابق الأعمدة</h3>";

if ($result['success']) {
    $file_headers = $result['headers'];
    
    echo "<p><strong>تطابق محتمل:</strong></p>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>عمود الملف</th><th>حقل النظام المقترح</th></tr>";
    
    foreach ($file_headers as $file_header) {
        $suggested_field = '';
        
        // اقتراح تطابق بناءً على النص
        foreach ($import_columns as $column) {
            if (stripos($column['title'], $file_header) !== false || 
                stripos($file_header, $column['title']) !== false) {
                $suggested_field = $column['field'] . ' (' . $column['title'] . ')';
                break;
            }
        }
        
        // اقتراحات يدوية
        if (empty($suggested_field)) {
            if (stripos($file_header, 'اسم') !== false && stripos($file_header, 'مورد') !== false) {
                $suggested_field = 'G_name_ar (اسم المورد)';
            } elseif (stripos($file_header, 'شركة') !== false) {
                $suggested_field = 'S_company_name (اسم الشركة)';
            } elseif (stripos($file_header, 'هاتف') !== false) {
                $suggested_field = 'G_phone (الهاتف)';
            } elseif (stripos($file_header, 'بريد') !== false || stripos($file_header, 'email') !== false) {
                $suggested_field = 'S_email (البريد الإلكتروني)';
            }
        }
        
        echo "<tr>";
        echo "<td><strong>" . htmlspecialchars($file_header) . "</strong></td>";
        echo "<td>" . htmlspecialchars($suggested_field ?: 'لا يوجد اقتراح') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// تنظيف
unlink($test_file);

echo "<hr>";
echo "<h3>🎯 الخلاصة</h3>";
echo "<p>إذا كانت جميع الاختبارات تعمل بشكل صحيح، فالمشكلة قد تكون في:</p>";
echo "<ul>";
echo "<li>📁 <strong>مسار الملفات</strong> - تأكد من أن المسارات صحيحة</li>";
echo "<li>🔗 <strong>JavaScript</strong> - تحقق من console.log في المتصفح</li>";
echo "<li>🗂️ <strong>الجلسة</strong> - تأكد من أن بيانات الجلسة محفوظة</li>";
echo "<li>🔄 <strong>التطابق</strong> - تأكد من أن تطابق الأعمدة يتم بشكل صحيح</li>";
echo "</ul>";

echo "<h4>خطوات التشخيص:</h4>";
echo "<ol>";
echo "<li>افتح أدوات المطور في المتصفح (F12)</li>";
echo "<li>اذهب إلى تبويب Console</li>";
echo "<li>جرب رفع ملف CSV</li>";
echo "<li>راقب الرسائل في console.log</li>";
echo "<li>تحقق من البيانات المرسلة والمستلمة</li>";
echo "</ol>";
?>
