/**
 * التطبيق الأساسي - وظائف عامة ومشتركة
 * Core Application - Common functions and utilities
 */

(function() {
    'use strict';

    // انتظار تحميل DOM
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initApp);
    } else {
        initApp();
    }

    function initApp() {
        console.log('🚀 بدء تهيئة التطبيق الأساسي...');

        // تهيئة المكونات الأساسية
        initThemeSystem();
        initLanguageSystem();
        initScrollSystem();
        initScrollbarStyles();
        initCustomScrollbar();
        initAjaxHelpers();
        initToastrConfig();

        console.log('✅ تم تهيئة التطبيق الأساسي بنجاح');
    }

    // ===== نظام الثيم ===== //
    function initThemeSystem() {
        const themeToggles = document.querySelectorAll('[data-toggle="theme"]');

        themeToggles.forEach(function(toggle) {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                const theme = this.getAttribute('data-theme');
                const url = window.APP_CONFIG.APP_URL + '/settings/theme/' + theme;

                // تطبيق التغيير فوراً
                if (theme === 'dark') {
                    document.body.classList.add('dark-theme');
                    this.setAttribute('data-theme', 'light');
                    this.innerHTML = '<i class="fas fa-sun me-2"></i> الوضع الفاتح';
                } else {
                    document.body.classList.remove('dark-theme');
                    this.setAttribute('data-theme', 'dark');
                    this.innerHTML = '<i class="fas fa-moon me-2"></i> الوضع الداكن';
                }

                // تحديث الإعدادات في قاعدة البيانات
                sendAjaxRequest(url);
            });
        });
    }

    // ===== نظام اللغة ===== //
    function initLanguageSystem() {
        const languageSwitches = document.querySelectorAll('.language-switch');
        
        languageSwitches.forEach(function(langSwitch) {
            langSwitch.addEventListener('click', function(e) {
                const lang = this.getAttribute('data-lang');
                
                // تطبيق التغييرات فوراً
                if (lang === 'ar') {
                    document.body.classList.add('rtl');
                    document.documentElement.setAttribute('dir', 'rtl');
                } else {
                    document.body.classList.remove('rtl');
                    document.documentElement.setAttribute('dir', 'ltr');
                }
            });
        });
    }



    // ===== نظام التمرير ===== //
    function initScrollSystem() {
        const scrollToTopBtn = document.getElementById('scroll-to-top');
        
        if (scrollToTopBtn) {
            // إظهار/إخفاء زر العودة للأعلى
            window.addEventListener('scroll', function() {
                if (window.scrollY > 300) {
                    scrollToTopBtn.classList.add('visible');
                } else {
                    scrollToTopBtn.classList.remove('visible');
                }
            });

            // العودة للأعلى عند النقر
            scrollToTopBtn.addEventListener('click', function() {
                window.scrollTo({ top: 0, behavior: 'smooth' });
            });
        }
    }

    // ===== مساعدات AJAX ===== //
    function initAjaxHelpers() {
        // دالة عامة لإرسال طلبات AJAX
        window.sendAjaxRequest = function(url, options = {}) {
            const defaults = {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/json'
                }
            };

            const config = Object.assign({}, defaults, options);

            return fetch(url, config)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .catch(error => {
                    console.error('AJAX Error:', error);
                    if (window.toastr) {
                        toastr.error('حدث خطأ في الاتصال');
                    }
                });
        };
    }

    // ===== إعدادات Toastr ===== //
    function initToastrConfig() {
        if (window.toastr) {
            toastr.options = {
                closeButton: true,
                debug: false,
                newestOnTop: true,
                progressBar: true,
                positionClass: window.APP_CONFIG.IS_RTL ? 'toast-top-left' : 'toast-top-right',
                preventDuplicates: true,
                onclick: null,
                showDuration: '300',
                hideDuration: '1000',
                timeOut: '5000',
                extendedTimeOut: '1000',
                showEasing: 'swing',
                hideEasing: 'linear',
                showMethod: 'fadeIn',
                hideMethod: 'fadeOut'
            };
        }
    }

    // ===== وظائف مساعدة عامة ===== //
    
    // تنسيق الأرقام
    window.formatNumber = function(number, decimals = 2) {
        return new Intl.NumberFormat(window.APP_CONFIG.LANG === 'ar' ? 'ar-SA' : 'en-US', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        }).format(number);
    };

    // تنسيق العملة
    window.formatCurrency = function(amount, currency = 'SAR') {
        return new Intl.NumberFormat(window.APP_CONFIG.LANG === 'ar' ? 'ar-SA' : 'en-US', {
            style: 'currency',
            currency: currency
        }).format(amount);
    };

    // تنسيق التاريخ
    window.formatDate = function(date, options = {}) {
        const defaults = {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };
        
        const config = Object.assign({}, defaults, options);
        
        return new Intl.DateTimeFormat(window.APP_CONFIG.LANG === 'ar' ? 'ar-SA' : 'en-US', config)
            .format(new Date(date));
    };

    // إظهار رسالة نجاح
    window.showSuccess = function(message, title = 'نجح') {
        if (window.toastr) {
            toastr.success(message, title);
        }
    };

    // إظهار رسالة خطأ
    window.showError = function(message, title = 'خطأ') {
        if (window.toastr) {
            toastr.error(message, title);
        }
    };

    // إظهار رسالة تحذير
    window.showWarning = function(message, title = 'تحذير') {
        if (window.toastr) {
            toastr.warning(message, title);
        }
    };

    // إظهار رسالة معلومات
    window.showInfo = function(message, title = 'معلومات') {
        if (window.toastr) {
            toastr.info(message, title);
        }
    };

    // ===== أنماط شريط التمرير ===== //
    function initScrollbarStyles() {
        // دالة لتطبيق أنماط شريط التمرير حسب اللغة
        function applyScrollbarStyles() {
            const isRTL = document.body.classList.contains('rtl');

            if (isRTL) {
                // تطبيق أنماط للغة العربية (شريط التمرير على اليسار)
                document.documentElement.style.overflowY = 'auto';
                document.documentElement.style.direction = 'rtl';
            } else {
                // تطبيق أنماط للغة الإنجليزية (شريط التمرير على اليمين)
                document.documentElement.style.overflowY = 'auto';
                document.documentElement.style.direction = 'ltr';
            }
        }

        // تطبيق أنماط شريط التمرير عند تحميل الصفحة
        applyScrollbarStyles();
    }

    // ===== شريط التمرير المخصص ===== //
    function initCustomScrollbar() {
        const scrollbarThumb = document.getElementById('scrollbar-thumb');
        const customScrollbar = document.getElementById('custom-scrollbar');

        if (!scrollbarThumb || !customScrollbar) return;

        // حساب نسبة ارتفاع شريط التمرير
        function updateScrollbarThumb() {
            const windowHeight = window.innerHeight;
            const documentHeight = document.documentElement.scrollHeight;

            // التحقق مما إذا كانت هناك حاجة لشريط التمرير
            if (documentHeight <= windowHeight) {
                customScrollbar.style.display = 'none';
                return;
            } else {
                customScrollbar.style.display = 'block';
            }

            // حساب نسبة ارتفاع الشريط
            const scrollThumbHeight = (windowHeight / documentHeight) * windowHeight;
            const minHeight = 30;
            scrollbarThumb.style.height = Math.max(scrollThumbHeight, minHeight) + 'px';

            updateScrollbarPosition();
        }

        // تحديث موضع شريط التمرير
        function updateScrollbarPosition() {
            const windowHeight = window.innerHeight;
            const documentHeight = document.documentElement.scrollHeight;

            if (documentHeight <= windowHeight) {
                customScrollbar.style.display = 'none';
                return;
            } else {
                customScrollbar.style.display = 'block';
            }

            const scrollPercentage = window.scrollY / (documentHeight - windowHeight);
            const maxScrollTop = windowHeight - parseFloat(scrollbarThumb.style.height || '30px');
            const scrollTop = scrollPercentage * maxScrollTop;

            if (isNaN(scrollTop) || !isFinite(scrollTop)) {
                return;
            }

            scrollbarThumb.style.top = scrollTop + 'px';
        }

        // Event listeners
        window.addEventListener('scroll', updateScrollbarPosition);
        window.addEventListener('resize', updateScrollbarThumb);
        window.addEventListener('load', updateScrollbarThumb);

        // مراقبة التغييرات في محتوى الصفحة
        const observer = new MutationObserver(function(mutations) {
            updateScrollbarThumb();
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['style', 'class']
        });

        // تهيئة شريط التمرير
        updateScrollbarThumb();
        setTimeout(updateScrollbarThumb, 500);

        // تفاعل الشريط
        scrollbarThumb.addEventListener('mouseenter', function() {
            this.style.opacity = '0.9';
        });

        scrollbarThumb.addEventListener('mouseleave', function() {
            this.style.opacity = '0.7';
        });

        // تعديل موضع الشريط حسب اللغة
        const isRTL = document.body.classList.contains('rtl') || document.documentElement.dir === 'rtl';
        if (isRTL) {
            scrollbarThumb.style.right = 'auto';
            scrollbarThumb.style.left = '2px';
            customScrollbar.style.right = 'auto';
            customScrollbar.style.left = '0';
        } else {
            scrollbarThumb.style.left = 'auto';
            scrollbarThumb.style.right = '2px';
            customScrollbar.style.left = 'auto';
            customScrollbar.style.right = '0';
        }

        scrollbarThumb.style.pointerEvents = 'auto';

        // وظيفة السحب والإفلات
        let isDragging = false;
        let startY = 0;
        let startScrollY = 0;

        scrollbarThumb.addEventListener('mousedown', function(e) {
            isDragging = true;
            startY = e.clientY;
            startScrollY = window.scrollY;
            document.body.style.userSelect = 'none';
        });

        document.addEventListener('mousemove', function(e) {
            if (!isDragging) return;

            const deltaY = e.clientY - startY;
            const scrollRatio = document.documentElement.scrollHeight / window.innerHeight;
            window.scrollTo(0, startScrollY + deltaY * scrollRatio);
        });

        document.addEventListener('mouseup', function() {
            isDragging = false;
            document.body.style.userSelect = '';
        });
    }

    // تصدير الدوال للنطاق العام
    window.initCustomScrollbar = initCustomScrollbar;
    window.initKeyboardShortcuts = function() {
        // هذه الدالة موجودة في topbar.js
        if (window.TopbarSystem && window.TopbarSystem.initKeyboardShortcuts) {
            window.TopbarSystem.initKeyboardShortcuts();
        }
    };

})();
