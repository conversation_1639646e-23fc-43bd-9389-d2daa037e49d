 body.dark-theme .topbar {
            background-color: var(--dark-topbar-bg);
            border-color: var(--dark-border-color);
        }



        body.dark-theme .topbar-search input {
            background-color: var(--dark-bg-color);
            border-color: var(--dark-border-color);
            color: var(--dark-text-color);
        }

        body.dark-theme .topbar-action {
            color: #aaa;
        }

        body.dark-theme .topbar-user-name {
            color: var(--dark-text-color);
        }

        body.dark-theme .topbar-user-role {
            color: #aaa;
        }



        /* Dark theme support for topbar dropdown */
        body.dark-theme .topbar-dropdown-menu {
            background-color: var(--dark-card-bg);
            border-color: var(--dark-border-color);
        }

        body.dark-theme .topbar-dropdown-item {
            color: var(--dark-text-color);
        }

        body.dark-theme .topbar-dropdown-item:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        body.dark-theme .topbar-dropdown-divider {
            border-color: var(--dark-border-color);
        }

  /* Topbar Styles - Modern & Sleek */
        .topbar {
            background-color: var(--light-topbar-bg);
            height: var(--topbar-height);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 var(--spacing-6);
            box-shadow: var(--box-shadow-sm);
            position: sticky;
            top: 0;
            z-index: 999;
            transition: all var(--transition-normal) var(--transition-bezier);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--light-border-color);
        }

        body.dark-theme .topbar {
            background-color: var(--dark-topbar-bg);
            border-color: var(--dark-border-color);
        }

       /* ===== MOBILE SIDEBAR TOGGLE - PROFESSIONAL DESIGN ===== */
        .mobile-sidebar-toggle {
            display: none;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border: none;
            color: white;
            font-size: 1.25rem;
            cursor: pointer;
            padding: var(--spacing-3);
            border-radius: var(--border-radius-lg);
            transition: all var(--transition-normal) var(--transition-bezier);
            box-shadow: var(--box-shadow-sm);
            position: relative;
            overflow: hidden;
            width: 3rem;
            height: 3rem;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .mobile-sidebar-toggle::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
            opacity: 0;
            transition: opacity var(--transition-normal) var(--transition-bezier);
            border-radius: var(--border-radius-lg);
        }

        .mobile-sidebar-toggle:hover::before {
            opacity: 1;
        }

        .mobile-sidebar-toggle:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-lg);
            background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
        }

        .mobile-sidebar-toggle:active {
            transform: translateY(0);
            box-shadow: var(--box-shadow-sm);
        }

        .mobile-sidebar-toggle i {
            position: relative;
            z-index: 1;
            transition: all var(--transition-normal) var(--transition-bezier);
        }

        .mobile-sidebar-toggle:hover i {
            transform: scale(1.1);
        }

        /* Dark theme styles */
        body.dark-theme .mobile-sidebar-toggle {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        body.dark-theme .mobile-sidebar-toggle:hover {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
        }

        /* Focus state for accessibility */
        .mobile-sidebar-toggle:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
        }

        .mobile-sidebar-toggle:focus-visible {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }

        /* Animation for the hamburger icon */
        .mobile-sidebar-toggle:hover i {
            animation: hamburgerPulse 0.6s ease-in-out;
        }

        @keyframes hamburgerPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.2); }
        }

        /* Show on mobile devices */
        @media (max-width: 768px) {
            .mobile-sidebar-toggle {
                display: flex;
                margin-right: var(--spacing-3);
            }

            .rtl .mobile-sidebar-toggle {
                margin-right: 0;
                margin-left: var(--spacing-3);
            }
        }

        @media (max-width: 480px) {
            .mobile-sidebar-toggle {
                width: 2.75rem;
                height: 2.75rem;
                font-size: 1.1rem;
                padding: var(--spacing-2);
            }
        }

        /* Loading state */
        .mobile-sidebar-toggle.loading i {
            animation: spin 1s linear infinite;
        }

        /* Pressed state animation */
        .mobile-sidebar-toggle.pressed {
            animation: pressEffect 0.2s ease-out;
        }

        @keyframes pressEffect {
            0% { transform: scale(1); }
            50% { transform: scale(0.95); }
            100% { transform: scale(1); }
        }

        /* Ripple effect */
        .mobile-sidebar-toggle::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.3s ease, height 0.3s ease;
        }

        .mobile-sidebar-toggle:active::after {
            width: 100%;
            height: 100%;
        }


        /* Topbar Actions - التحكم في الموضع حسب اللغة */
        .topbar-actions {
            display: flex;
            align-items: center;
        }

        /* للغة الإنجليزية: على اليمين */
        body:not(.rtl) .topbar-actions {
            margin-left: auto;
        }

        /* للغة العربية: على اليسار */
        body.rtl .topbar-actions {
            margin-right: auto;
        }

        .topbar-action {
            position: relative;
            margin-left: var(--spacing-5);
            color: var(--gray-700);
            font-size: 1.25rem;
            cursor: pointer;
            transition: all var(--transition-normal) var(--transition-bezier);
            width: 2.5rem;
            height: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--border-radius);
            overflow: hidden;
            border: 1px solid var(--light-input-border);
        }

        .topbar-action::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: var(--light-hover-bg);
            transform: scale(0);
            transition: transform var(--transition-fast) var(--transition-bezier);
            border-radius: var(--border-radius);
        }

        .topbar-action:hover::before {
            transform: scale(1);
        }

        .topbar-action i {
            position: relative;
            z-index: 1;
            transition: all var(--transition-normal) var(--transition-bezier);
        }

        body.dark-theme .topbar-action {
            color: var(--gray-400);
        }

        .rtl .topbar-action {
            margin-left: 0;
            margin-right: var(--spacing-5);
        }

        .topbar-action:hover i {
            color: var(--primary-color);
            transform: translateY(-1px);
        }

        body.dark-theme .topbar-action:hover i {
            color: var(--primary-light);
        }

        .topbar-action-badge {
            position: absolute;
            top: 0.25rem;
            right: 0.25rem;
            background: var(--danger-gradient);
            color: white;
            font-size: 0.7rem;
            min-width: 1.125rem;
            height: 1.125rem;
            border-radius: var(--border-radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 var(--spacing-1);
            font-weight: 600;
            box-shadow: 0 0 0 2px var(--light-topbar-bg);
            z-index: 2;
        }

        body.dark-theme .topbar-action-badge {
            box-shadow: 0 0 0 2px var(--dark-topbar-bg);
        }

        .rtl .topbar-action-badge {
            right: auto;
            left: 0.25rem;
        }

        .topbar-user {
            display: flex;
            align-items: center;
            margin-left: var(--spacing-6);
            cursor: pointer;
            padding: var(--spacing-2);
            border-radius: var(--border-radius);
            transition: all var(--transition-normal) var(--transition-bezier);
            position: relative;
            overflow: hidden;
        }

        .topbar-user::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: var(--light-hover-bg);
            transform: scale(0);
            transition: transform var(--transition-fast) var(--transition-bezier);
            border-radius: var(--border-radius);
        }

        .topbar-user:hover::before {
            transform: scale(1);
        }

        body.dark-theme .topbar-user::before {
            background-color: var(--dark-hover-bg);
        }

        .rtl .topbar-user {
            margin-left: 0;
            margin-right: var(--spacing-6);
        }

        .topbar-user-avatar {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: var(--border-radius-full);
            overflow: hidden;
            margin-right: var(--spacing-3);
            border: 2px solid var(--primary-light);
            box-shadow: var(--box-shadow-sm);
            position: relative;
            z-index: 1;
            transition: all var(--transition-normal) var(--transition-bezier);
        }

        .topbar-user:hover .topbar-user-avatar {
            transform: scale(1.05);
            box-shadow: var(--box-shadow);
        }

        .rtl .topbar-user-avatar {
            margin-right: 0;
            margin-left: var(--spacing-3);
        }

        .topbar-user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: all var(--transition-normal) var(--transition-bezier);
        }

        .topbar-user:hover .topbar-user-avatar img {
            transform: scale(1.1);
        }

        .topbar-user-info {
            display: flex;
            flex-direction: column;
            position: relative;
            z-index: 1;
        }

        .topbar-user-name {
            font-weight: 600;
            font-size: 0.9rem;
            color: var(--dark-color);
            transition: all var(--transition-normal) var(--transition-bezier);
        }

        body.dark-theme .topbar-user-name {
            color: var(--dark-text-color);
        }

        .topbar-user:hover .topbar-user-name {
            color: var(--primary-color);
        }

        body.dark-theme .topbar-user:hover .topbar-user-name {
            color: var(--primary-light);
        }

        .topbar-user-role {
            font-size: 0.75rem;
            color: var(--gray-600);
            transition: all var(--transition-normal) var(--transition-bezier);
        }

        body.dark-theme .topbar-user-role {
            color: var(--gray-500);
        }

        /* Enhanced User Dropdown Arrow */
        .topbar-user-dropdown {
            margin-left: var(--spacing-2);
            color: var(--gray-500);
            font-size: 0.875rem;
            transition: all var(--transition-normal) var(--transition-bezier);
            position: relative;
            z-index: 1;
        }

        .topbar-user:hover .topbar-user-dropdown {
            transform: translateY(2px);
        }

        .rtl .topbar-user-dropdown {
            margin-left: 0;
            margin-right: var(--spacing-2);
        }

        /* Arrow rotation when topbar dropdown is open */
        .topbar-dropdown.show .topbar-user-dropdown {
            transform: rotate(180deg);
            color: var(--primary-color);
        }

        body.dark-theme .topbar-dropdown.show .topbar-user-dropdown {
            color: var(--primary-light);
        }

        /* Topbar Dropdown Styles - نسخة مطابقة للتصميم الأصلي */
        .topbar-dropdown-menu {
            border-radius: var(--border-radius-md);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: var(--box-shadow-lg), 0 0 15px rgba(99, 102, 241, 0.1);
            padding: var(--spacing-3) var(--spacing-2);
            min-width: 16rem;
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            opacity: 0;
            transform: translateY(-10px);
            visibility: hidden;
            pointer-events: none;
            position: absolute;
            z-index: 1000;
        }

        .topbar-dropdown.show .topbar-dropdown-menu {
            opacity: 1;
            transform: translateY(0);
            visibility: visible;
            pointer-events: auto;
        }

        /* Topbar Dropdown arrow */
        .topbar-dropdown-menu::before {
            content: '';
            position: absolute;
            top: -6px;
            right: 20px;
            width: 12px;
            height: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-bottom: none;
            border-right: none;
            background-color: rgba(255, 255, 255, 0.95);
            transform: rotate(-45deg);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            z-index: -1;
        }

        .rtl .topbar-dropdown-menu::before {
            right: auto;
            left: 20px;
        }

        body.dark-theme .topbar-dropdown-menu {
            border-color: rgba(255, 255, 255, 0.05);
            box-shadow: var(--box-shadow-lg), 0 0 15px rgba(99, 102, 241, 0.15);
        }

        body.dark-theme .topbar-dropdown-menu::before {
            background-color: rgba(31, 41, 55, 0.95);
            border-color: rgba(255, 255, 255, 0.05);
        }

        .topbar-dropdown-item {
            padding: var(--spacing-3) var(--spacing-5);
            font-size: 0.9rem;
            color: var(--gray-700);
            transition: all 0.2s var(--transition-bezier);
            position: relative;
            text-decoration: none;
            border: none;
            background: none;
            width: 100%;
            display: flex;
            align-items: center;
            transform: translateY(10px);
            opacity: 0;
        }

        .topbar-dropdown.show .topbar-dropdown-item {
            transform: translateY(0);
            opacity: 1;
        }

        body.dark-theme .topbar-dropdown-item {
            color: var(--gray-300);
        }

        .topbar-dropdown-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: var(--light-hover-bg);
            opacity: 0;
            transition: opacity 0.2s var(--transition-bezier);
            z-index: 0;
            border-radius: var(--border-radius);
        }

        body.dark-theme .topbar-dropdown-item::before {
            background-color: var(--dark-hover-bg);
        }

        .topbar-dropdown-item:hover::before {
            opacity: 1;
        }

        .topbar-dropdown-item:hover {
            color: var(--primary-color);
            transform: translateX(3px);
        }

        .rtl .topbar-dropdown-item:hover {
            transform: translateX(-3px);
        }

        body.dark-theme .topbar-dropdown-item:hover {
            color: var(--primary-light);
        }

        .topbar-dropdown-item i {
            margin-right: var(--spacing-3);
            font-size: 1.1rem;
            width: 1.5rem;
            height: 1.5rem;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 1;
            transition: all 0.2s var(--transition-bezier);
            border-radius: var(--border-radius-sm);
            background-color: rgba(99, 102, 241, 0.1);
            color: var(--primary-color);
        }

        body.dark-theme .topbar-dropdown-item i {
            background-color: rgba(99, 102, 241, 0.2);
            color: var(--primary-light);
        }

        .topbar-dropdown-item:hover i {
            transform: scale(1.1);
            background-color: var(--primary-color);
            color: white;
        }

        body.dark-theme .topbar-dropdown-item:hover i {
            background-color: var(--primary-light);
        }

        .rtl .topbar-dropdown-item i {
            margin-right: 0;
            margin-left: var(--spacing-3);
        }

        .topbar-dropdown-item span {
            position: relative;
            z-index: 1;
            font-weight: 500;
            transition: all 0.2s var(--transition-bezier);
        }

        .topbar-dropdown-item:hover span {
            font-weight: 600;
        }

        /* Topbar Dropdown divider */
        .topbar-dropdown-divider {
            margin: var(--spacing-3) var(--spacing-4);
            border: none;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--light-border-color), transparent);
            opacity: 0.5;
            position: relative;
        }

        .topbar-dropdown-divider::before {
            content: '';
            position: absolute;
            top: -3px;
            left: 50%;
            transform: translateX(-50%);
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: var(--light-border-color);
            opacity: 0.7;
        }

        body.dark-theme .topbar-dropdown-divider {
            background: linear-gradient(to right, transparent, var(--dark-border-color), transparent);
        }

        body.dark-theme .topbar-dropdown-divider::before {
            background-color: var(--dark-border-color);
        }

        /* Topbar Dropdown header */
        .topbar-dropdown-header {
            padding: var(--spacing-2) var(--spacing-5);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            color: var(--gray-500);
            margin-bottom: var(--spacing-2);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .topbar-dropdown-header::after {
            content: '';
            height: 1px;
            width: 30px;
            background: linear-gradient(to right, var(--primary-color), transparent);
            margin-left: var(--spacing-2);
        }

        .rtl .topbar-dropdown-header::after {
            background: linear-gradient(to left, var(--primary-color), transparent);
            margin-left: 0;
            margin-right: var(--spacing-2);
        }

        body.dark-theme .topbar-dropdown-header {
            color: var(--gray-400);
        }

        /* Topbar Dropdown footer */
        .topbar-dropdown-footer {
            padding: var(--spacing-3) var(--spacing-5);
            margin-top: var(--spacing-2);
            border-top: 1px solid var(--light-border-color);
            font-size: 0.85rem;
            color: var(--gray-600);
            text-align: center;
        }

        body.dark-theme .topbar-dropdown-footer {
            border-color: var(--dark-border-color);
            color: var(--gray-500);
        }

        /* Topbar Dropdown with icons on right */
        .topbar-dropdown-item-with-icon-right {
            justify-content: space-between;
        }

        .topbar-dropdown-item-with-icon-right .topbar-dropdown-item-icon-right {
            font-size: 0.9rem;
            color: var(--gray-500);
            transition: all 0.2s var(--transition-bezier);
        }

        .topbar-dropdown-item-with-icon-right:hover .topbar-dropdown-item-icon-right {
            color: var(--primary-color);
            transform: translateX(3px);
        }

        .rtl .topbar-dropdown-item-with-icon-right:hover .topbar-dropdown-item-icon-right {
            transform: translateX(-3px);
        }

        body.dark-theme .topbar-dropdown-item-with-icon-right .topbar-dropdown-item-icon-right {
            color: var(--gray-600);
        }

        body.dark-theme .topbar-dropdown-item-with-icon-right:hover .topbar-dropdown-item-icon-right {
            color: var(--primary-light);
        }

        /* Main Content Styles */
        .main-content {
            padding: 1.5rem;
            flex: 1;
            transition: all var(--transition-speed) ease-in-out;
        }

        .page-title {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: var(--dark-color);
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .page-title i {
            color: var(--primary-color);
            font-size: 1.5rem;
        }

        /* Dashboard Widgets */
        .widget {
            border-radius: var(--border-radius);
            padding: 1.5rem;
            height: 100%;
            transition: all var(--transition-speed) ease-in-out;
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .widget-icon {
            position: absolute;
            top: 1.5rem;
            right: 1.5rem;
            font-size: 2.5rem;
            opacity: 0.15;
            color: inherit;
        }

        .rtl .widget-icon {
            right: auto;
            left: 1.5rem;
        }

        .widget-title {
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 1rem;
            color: inherit;
            opacity: 0.8;
        }

        .widget-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: inherit;
        }

        .widget-subtitle {
            font-size: 0.875rem;
            color: inherit;
            opacity: 0.7;
        }

        .widget-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .widget-secondary {
            background-color: var(--secondary-color);
            color: white;
        }

        .widget-success {
            background-color: var(--success-color);
            color: white;
        }

        .widget-info {
            background-color: var(--info-color);
            color: white;
        }

        .widget-warning {
            background-color: var(--warning-color);
            color: white;
        }

        .widget-danger {
            background-color: var(--danger-color);
            color: white;
        }

        .widget-light {
            background-color: var(--light-card-bg);
            color: var(--dark-color);
            border: 1px solid var(--light-border-color);
        }

        .widget-dark {
            background-color: var(--dark-color);
            color: white;
        }