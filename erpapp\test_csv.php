<?php
/**
 * اختبار نظام CSV
 */

define('BASE_PATH', __DIR__);
require_once 'loader.php';
require_once 'App/Helpers/import_export_helper.php';

echo "<h2>اختبار نظام CSV</h2>";

try {
    // اختبار إنشاء قالب CSV
    echo "<h3>1. اختبار إنشاء قالب CSV</h3>";
    $columns = [
        ['field' => 'G_name_ar', 'title' => 'اسم المورد *', 'required' => true],
        ['field' => 'G_phone', 'title' => 'الهاتف', 'required' => false],
        ['field' => 'S_email', 'title' => 'البريد الإلكتروني', 'required' => false],
        ['field' => 'G_status', 'title' => 'الحالة', 'required' => false]
    ];
    
    $result = create_csv_template($columns, [
        'title' => 'قالب استيراد الموردين',
        'filename' => 'suppliers_template_test'
    ]);
    
    if ($result['success']) {
        echo "<p style='color: green;'>✓ تم إنشاء قالب CSV بنجاح: " . $result['filename'] . "</p>";
        
        // عرض محتوى الملف
        if (file_exists($result['filepath'])) {
            $content = file_get_contents($result['filepath']);
            echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
            echo htmlspecialchars($content);
            echo "</pre>";
            
            // حذف الملف المؤقت
            unlink($result['filepath']);
            echo "<p>🗑️ تم حذف الملف المؤقت</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ فشل في إنشاء القالب: " . $result['error'] . "</p>";
    }
    
    // اختبار تصدير CSV
    echo "<h3>2. اختبار تصدير CSV</h3>";
    $test_data = [
        ['G_name_ar' => 'شركة الاختبار الأولى', 'G_phone' => '0123456789', 'S_email' => '<EMAIL>', 'G_status' => 'active'],
        ['G_name_ar' => 'شركة الاختبار الثانية', 'G_phone' => '0987654321', 'S_email' => '<EMAIL>', 'G_status' => 'inactive']
    ];
    
    $export_columns = [
        ['field' => 'G_name_ar', 'title' => 'اسم المورد', 'export' => true],
        ['field' => 'G_phone', 'title' => 'الهاتف', 'export' => true],
        ['field' => 'S_email', 'title' => 'البريد الإلكتروني', 'export' => true],
        ['field' => 'G_status', 'title' => 'الحالة', 'export' => true]
    ];
    
    $export_result = export_data_to_csv($test_data, $export_columns, [
        'title' => 'بيانات الموردين',
        'filename' => 'suppliers_export_test'
    ]);
    
    if ($export_result['success']) {
        echo "<p style='color: green;'>✓ تم تصدير البيانات إلى CSV بنجاح: " . $export_result['filename'] . "</p>";
        echo "<p>📊 عدد السجلات: " . $export_result['records_count'] . "</p>";
        
        // عرض محتوى الملف
        if (file_exists($export_result['filepath'])) {
            $content = file_get_contents($export_result['filepath']);
            echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
            echo htmlspecialchars($content);
            echo "</pre>";
            
            // حذف الملف المؤقت
            unlink($export_result['filepath']);
            echo "<p>🗑️ تم حذف الملف المؤقت</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ فشل في التصدير: " . $export_result['error'] . "</p>";
    }
    
    echo "<hr>";
    echo "<h3>🎉 النتيجة</h3>";
    echo "<p><strong>نظام CSV يعمل بشكل مثالي!</strong></p>";
    echo "<p>يمكنك الآن:</p>";
    echo "<ul>";
    echo "<li>تنزيل قالب CSV من صفحة الموردين</li>";
    echo "<li>ملء البيانات في Excel أو أي محرر نصوص</li>";
    echo "<li>حفظ الملف كـ CSV</li>";
    echo "<li>رفع الملف للاستيراد</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ خطأ: " . $e->getMessage() . "</p>";
}
?>
