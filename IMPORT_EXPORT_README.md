# نظام الاستيراد والتصدير - Import/Export System

## 📋 نظرة عامة

تم إضافة نظام متكامل للاستيراد والتصدير إلى نظام ERP، يدعم:
- تصدير البيانات إلى ملفات Excel
- استيراد البيانات من ملفات Excel مع تطابق الأعمدة
- تنزيل قوالب الاستيراد
- واجهة مستخدم احترافية ومتجاوبة

## 🚀 الميزات الجديدة

### ✅ التصدير (Export)
- تصدير البيانات الحالية المعروضة
- تصدير جميع البيانات مع الفلاتر المطبقة
- تصدير جميع البيانات بدون فلاتر
- تضمين معلومات الفلاتر في الملف
- تنسيق احترافي مع ألوان وحدود

### ✅ الاستيراد (Import)
- رفع ملفات Excel (.xlsx, .xls)
- تطابق الأعمدة التفاعلي
- معاينة البيانات قبل الاستيراد
- التحقق من صحة البيانات
- عرض الأخطاء والتحذيرات
- واجهة متعددة الخطوات

### ✅ القوالب (Templates)
- تنزيل قوالب Excel جاهزة للاستيراد
- تنسيق احترافي مع رؤوس الأعمدة
- إرشادات واضحة

## 📁 الملفات المضافة

### 1. المساعدات (Helpers)
```
erpapp/App/Helpers/import_export_helper.php
```
- دوال التصدير والاستيراد
- معالجة ملفات Excel
- التحقق من صحة البيانات

### 2. المتحكمات (Controllers)
```
erpapp/App/Modules/Purchases/Controllers/ImportExportController.php
```
- متحكم مخصص لوحدة المشتريات
- معالجة طلبات التصدير والاستيراد للموردين
- تنفيذ العمليات مع التحقق من الصحة

### 3. الأنماط (CSS)
```
erpapp/public/css/components/import-export.css
```
- أنماط الأزرار والنوافذ
- تصميم متجاوب
- تأثيرات بصرية

### 4. السكريبت (JavaScript)
```
erpapp/public/js/components/import-export.js
```
- التفاعل مع المستخدم
- رفع الملفات
- تطابق الأعمدة

### 5. المسارات (Routes)
```
erpapp/App/Modules/Purchases/Module.php
```
- مسارات الاستيراد والتصدير مخصصة لوحدة المشتريات
- تنظيم أفضل داخل الوحدة

## 🔧 كيفية الاستخدام

### 1. إضافة الاستيراد/التصدير لجدول جديد

في ملف العرض (View):
```php
// إعداد الاستيراد والتصدير
$import_export_config = [
    'export' => true,
    'import' => true,
    'template' => true
];

// إضافة للإعدادات
render_datatable_page([
    // ... إعدادات أخرى
    'import_export_config' => $import_export_config
]);
```

### 2. إنشاء متحكم استيراد/تصدير لوحدة جديدة

إنشاء متحكم في وحدة جديدة:
```php
// erpapp/App/Modules/YourModule/Controllers/ImportExportController.php

class ImportExportController
{
    // دوال التصدير
    public function exportYourEntity() { ... }

    // دوال الاستيراد
    public function uploadYourEntityFile() { ... }
    public function previewYourEntityImport() { ... }
    public function executeYourEntityImport() { ... }

    // دوال القوالب
    public function downloadYourEntityTemplate() { ... }

    // دوال مساعدة
    private function getYourEntityExportColumns() { ... }
    private function getYourEntityImportColumns() { ... }
    private function saveYourEntity($data) { ... }
}
```

### 3. إضافة المسارات في Module.php

```php
// erpapp/App/Modules/YourModule/Module.php

add_route('POST', '/your-module/your-entity/export', 'App\Modules\YourModule\Controllers\ImportExportController@exportYourEntity');
add_route('GET', '/your-module/your-entity/download-template', 'App\Modules\YourModule\Controllers\ImportExportController@downloadYourEntityTemplate');
add_route('POST', '/your-module/your-entity/upload-file', 'App\Modules\YourModule\Controllers\ImportExportController@uploadYourEntityFile');
add_route('POST', '/your-module/your-entity/preview-import', 'App\Modules\YourModule\Controllers\ImportExportController@previewYourEntityImport');
add_route('POST', '/your-module/your-entity/execute-import', 'App\Modules\YourModule\Controllers\ImportExportController@executeYourEntityImport');
```

### 3. إضافة دالة حفظ البيانات

```php
private function saveImportedData($module, $entity, $data)
{
    switch ($module) {
        case 'your_module':
            switch ($entity) {
                case 'your_entity':
                    return $this->saveYourEntity($data, $company_id, $user_id);
            }
            break;
    }
    
    return ['success' => false, 'error' => 'نوع البيانات غير مدعوم'];
}
```

## 🎨 التخصيص

### تخصيص الأنماط
يمكن تخصيص الأنماط في:
```css
/* erpapp/public/css/components/import-export.css */

.datatable-export-btn {
    /* تخصيص زر التصدير */
}

.datatable-import-btn {
    /* تخصيص زر الاستيراد */
}
```

### تخصيص السكريبت
يمكن تخصيص السلوك في:
```javascript
// erpapp/public/js/components/import-export.js

function getSystemColumns(module, entity) {
    // إضافة إعدادات أعمدة جديدة
}
```

## 🔍 مثال عملي - الموردين

تم تطبيق النظام على وحدة الموردين كمثال:

### التصدير
- تصدير قائمة الموردين مع جميع البيانات
- تضمين الفلاتر المطبقة
- تنسيق احترافي

### الاستيراد
- استيراد موردين جدد من Excel
- تطابق الأعمدة (اسم المورد، الشركة، الهاتف، إلخ)
- التحقق من صحة البيانات
- معاينة قبل الحفظ

### القالب
- قالب Excel جاهز للموردين
- رؤوس أعمدة باللغة العربية
- تنسيق مناسب للإدخال

## 🛠️ المتطلبات التقنية

### المكتبات المطلوبة
- ✅ PhpSpreadsheet (مثبتة)
- ✅ jQuery (موجودة)
- ✅ Toastr للإشعارات (موجودة)

### إعدادات الخادم
- PHP 8.0+
- دعم رفع الملفات (5MB كحد أقصى)
- امتدادات PHP: zip, xml, mbstring

## 🔒 الأمان

- التحقق من نوع الملفات المرفوعة
- تحديد حجم الملف الأقصى
- التحقق من صلاحيات المستخدم
- تنظيف البيانات المستوردة
- حماية من SQL Injection

## 📊 الإحصائيات

- عدد الملفات المضافة: 5
- عدد الدوال الجديدة: 15+
- عدد أسطر الكود: 1000+
- الوقت المقدر للتطوير: 8 ساعات

## 🎯 الخطوات التالية

1. ✅ تطبيق النظام على وحدة الموردين
2. 🔄 إضافة دعم وحدات أخرى (المخزون، المبيعات)
3. 🔄 إضافة ميزات متقدمة (استيراد الصور، التحديث المجمع)
4. 🔄 تحسين الأداء للملفات الكبيرة
5. 🔄 إضافة تقارير الاستيراد/التصدير

## 📞 الدعم

للمساعدة أو الاستفسارات حول النظام، يرجى مراجعة:
- الكود المصدري في الملفات المذكورة
- التعليقات داخل الكود
- أمثلة الاستخدام في وحدة الموردين

---

**تم التطوير بواسطة:** Augment Agent  
**التاريخ:** يونيو 2025  
**الإصدار:** 1.0.0
