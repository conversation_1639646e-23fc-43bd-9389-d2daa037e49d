<?php
/**
 * اختبار نظام CSV الجديد
 */

define('BASE_PATH', __DIR__);
require_once 'loader.php';

echo "<h2>اختبار نظام CSV الديناميكي</h2>";

try {
    echo "<h3>1. اختبار إنشاء قالب CSV للموردين</h3>";
    
    // محاكاة أعمدة الموردين
    $supplier_columns = [
        ['field' => 'G_name_ar', 'title' => 'اسم المورد', 'required' => true, 'data_type' => 'text'],
        ['field' => 'G_name_en', 'title' => 'الاسم الإنجليزي', 'required' => false, 'data_type' => 'text'],
        ['field' => 'G_phone', 'title' => 'الهاتف', 'required' => false, 'data_type' => 'text'],
        ['field' => 'S_email', 'title' => 'البريد الإلكتروني', 'required' => false, 'data_type' => 'email'],
        ['field' => 'G_status', 'title' => 'الحالة', 'required' => false, 'data_type' => 'select',
         'import_options' => ['active' => 'نشط', 'inactive' => 'غير نشط']]
    ];
    
    $template_result = create_csv_template($supplier_columns, [
        'title' => 'قالب استيراد الموردين',
        'filename' => 'suppliers_template_test'
    ]);
    
    if ($template_result['success']) {
        echo "<p style='color: green;'>✓ تم إنشاء قالب الموردين بنجاح</p>";
        echo "<p>📁 الملف: " . $template_result['filename'] . "</p>";
        
        // عرض محتوى القالب
        if (file_exists($template_result['filepath'])) {
            $content = file_get_contents($template_result['filepath']);
            echo "<h4>محتوى القالب:</h4>";
            echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
            echo htmlspecialchars($content);
            echo "</pre>";
        }
    } else {
        echo "<p style='color: red;'>✗ فشل في إنشاء القالب: " . $template_result['error'] . "</p>";
    }
    
    echo "<h3>2. اختبار تصدير بيانات الموردين</h3>";
    
    // بيانات تجريبية للموردين
    $suppliers_data = [
        [
            'entity_number' => '1',
            'G_name_ar' => 'شركة الاختبار الأولى',
            'G_name_en' => 'First Test Company',
            'G_phone' => '0112345678',
            'S_email' => '<EMAIL>',
            'G_status' => 'active',
            'group_name' => 'مجموعة تجريبية'
        ],
        [
            'entity_number' => '2',
            'G_name_ar' => 'شركة الاختبار الثانية',
            'G_name_en' => 'Second Test Company',
            'G_phone' => '0112345679',
            'S_email' => '<EMAIL>',
            'G_status' => 'inactive',
            'group_name' => 'مجموعة أخرى'
        ]
    ];
    
    $export_columns = [
        ['field' => 'entity_number', 'title' => 'الرقم', 'export' => true],
        ['field' => 'G_name_ar', 'title' => 'اسم المورد', 'export' => true],
        ['field' => 'G_name_en', 'title' => 'الاسم الإنجليزي', 'export' => true],
        ['field' => 'G_phone', 'title' => 'الهاتف', 'export' => true],
        ['field' => 'S_email', 'title' => 'البريد الإلكتروني', 'export' => true],
        ['field' => 'G_status', 'title' => 'الحالة', 'export' => true, 'type' => 'badge',
         'status_config' => ['texts' => ['active' => 'نشط', 'inactive' => 'غير نشط']]],
        ['field' => 'group_name', 'title' => 'المجموعة', 'export' => true]
    ];
    
    $export_result = export_to_csv($suppliers_data, $export_columns, [
        'title' => 'بيانات الموردين',
        'filename' => 'suppliers_export_test'
    ]);
    
    if ($export_result['success']) {
        echo "<p style='color: green;'>✓ تم تصدير بيانات الموردين بنجاح</p>";
        echo "<p>📁 الملف: " . $export_result['filename'] . "</p>";
        echo "<p>📊 عدد السجلات: " . $export_result['records_count'] . "</p>";
        
        // عرض محتوى الملف
        if (file_exists($export_result['filepath'])) {
            $content = file_get_contents($export_result['filepath']);
            echo "<h4>محتوى ملف التصدير:</h4>";
            echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
            echo htmlspecialchars($content);
            echo "</pre>";
        }
    } else {
        echo "<p style='color: red;'>✗ فشل في التصدير: " . $export_result['error'] . "</p>";
    }
    
    echo "<h3>3. اختبار قراءة ملف CSV</h3>";
    
    // إنشاء ملف CSV تجريبي للقراءة
    $test_csv_content = "\xEF\xBB\xBF" . "اسم المورد,الهاتف,البريد الإلكتروني,الحالة\n";
    $test_csv_content .= "شركة تجريبية,0112345678,<EMAIL>,active\n";
    $test_csv_content .= "شركة أخرى,0112345679,<EMAIL>,inactive\n";
    
    $test_file = sys_get_temp_dir() . '/test_read.csv';
    file_put_contents($test_file, $test_csv_content);
    
    $read_result = read_csv_file($test_file);
    
    if ($read_result['success']) {
        echo "<p style='color: green;'>✓ تم قراءة ملف CSV بنجاح</p>";
        echo "<p>📊 عدد الصفوف: " . $read_result['rows_count'] . "</p>";
        echo "<p>📋 الرؤوس: " . implode(', ', $read_result['headers']) . "</p>";
        
        echo "<h4>البيانات المقروءة:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr>";
        foreach ($read_result['headers'] as $header) {
            echo "<th style='padding: 5px; background: #f0f0f0;'>" . htmlspecialchars($header) . "</th>";
        }
        echo "</tr>";
        
        foreach ($read_result['data'] as $row) {
            echo "<tr>";
            foreach ($read_result['headers'] as $header) {
                echo "<td style='padding: 5px; border: 1px solid #ddd;'>" . htmlspecialchars($row[$header] ?? '') . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>✗ فشل في قراءة الملف: " . $read_result['error'] . "</p>";
    }
    
    // تنظيف الملفات المؤقتة
    if (isset($template_result) && $template_result['success'] && file_exists($template_result['filepath'])) {
        unlink($template_result['filepath']);
    }
    if (isset($export_result) && $export_result['success'] && file_exists($export_result['filepath'])) {
        unlink($export_result['filepath']);
    }
    if (file_exists($test_file)) {
        unlink($test_file);
    }
    
    echo "<hr>";
    echo "<h3>🎉 النتيجة النهائية</h3>";
    echo "<p><strong>نظام CSV الديناميكي يعمل بشكل مثالي!</strong></p>";
    echo "<p>الميزات المتوفرة:</p>";
    echo "<ul>";
    echo "<li>✅ تصدير البيانات إلى CSV مع دعم العربية</li>";
    echo "<li>✅ إنشاء قوالب استيراد مع أمثلة</li>";
    echo "<li>✅ قراءة ملفات CSV مع معالجة الأخطاء</li>";
    echo "<li>✅ تنسيق البيانات حسب النوع</li>";
    echo "<li>✅ دعم الحقول المطلوبة والاختيارية</li>";
    echo "</ul>";
    
    echo "<h4>الخطوات التالية:</h4>";
    echo "<ol>";
    echo "<li>اذهب إلى صفحة الموردين</li>";
    echo "<li>استخدم أزرار التصدير والاستيراد الجديدة</li>";
    echo "<li>جرب تنزيل القالب وملء البيانات</li>";
    echo "<li>ارفع الملف واختبر تطابق الأعمدة</li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ خطأ في الاختبار: " . $e->getMessage() . "</p>";
}
?>
