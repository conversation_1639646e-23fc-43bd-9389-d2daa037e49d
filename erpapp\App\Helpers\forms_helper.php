<?php
/**
 * Forms Helper Functions
 * مساعد النماذج - نظام منفصل ومتخصص
 *
 * هذا الملف مخصص للنماذج فقط
 * يتبع نفس نمط datatable_helper.php
 */

/**
 * عرض صفحة نموذج متخصصة
 * هذه الدالة مخصصة للنماذج فقط - تتبع نفس نمط render_datatable_page
 *
 * @param array $config إعدادات النموذج
 * @return void
 */
function render_form_page($config)
{
    // إعداد المتغيرات الأساسية للنماذج
    $title = $config['title'] ?? 'نموذج البيانات';
    $module = $config['module'] ?? 'default';
    $entity = $config['entity'] ?? 'items';

    // بيانات النموذج (مطلوبة)
    $form_config = $config['form'] ?? [];
    $form_data = $config['form_data'] ?? [];
    $readonly = $config['readonly'] ?? false; // وضع القراءة فقط

    // إعدادات اختيارية
    $actions = $config['actions'] ?? []; // الأزرار تأتي من ملف العرض
    $custom_content = $config['custom_content'] ?? null; // محتوى مخصص

    // Breadcrumb
    $breadcrumb = $config['breadcrumb'] ?? [
        ['title' => $title, 'active' => true]
    ];

    // عرض النموذج فقط - نفس طريقة datatable_helper
    render_form_layout($title, $module, $entity, $form_config, $form_data, $breadcrumb, $actions, $custom_content, $readonly);
}

/**
 * عرض تخطيط النموذج المتخصص
 * مخصص للنماذج فقط - نفس نظام datatable_helper
 */
function render_form_layout($title, $module, $entity, $form_config, $form_data, $breadcrumb, $actions, $custom_content, $readonly = false)
{
    ?>
    <div class="container-fluid">
        <!-- Header with Actions - محسن للنماذج -->
        <?php render_form_header($title, $breadcrumb, $actions); ?>

        <!-- Form Content -->
        <div class="row">
            <div class="col-12">
                <div class="card <?= $readonly ? 'readonly-mode' : '' ?>">
                    <div class="card-body">
                        <?php if ($custom_content && file_exists($custom_content)): ?>
                            <!-- محتوى مخصص للنماذج المعقدة -->
                            <?php include $custom_content; ?>
                        <?php elseif (!empty($form_config['tabs'])): ?>
                            <!-- نموذج بتبويبات ديناميكية -->
                            <?php render_tabbed_form($form_config, $form_data, $module, $entity); ?>
                        <?php elseif (!empty($form_config['fields'])): ?>
                            <!-- نموذج عادي بالحقول -->
                            <?php render_standard_form($form_config, $form_data, $module, $entity); ?>
                        <?php else: ?>
                            <!-- رسالة خطأ -->
                            <div class="alert alert-warning">
                                <i class="mdi mdi-alert-circle me-2"></i>
                                لم يتم تحديد محتوى النموذج أو الحقول المطلوبة.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <?php render_form_scripts($module, $entity); ?>
    <?php
}

/**
 * عرض نموذج بتبويبات ديناميكية
 */
function render_tabbed_form($form_config, $form_data, $module, $entity)
{
    $action = $form_config['action'] ?? base_url($module . '/' . $entity . '/store');
    $method = $form_config['method'] ?? 'POST';
    $form_id = $form_config['id'] ?? ($entity . 'Form');
    $tabs = $form_config['tabs'] ?? [];
    ?>
    
    <form action="<?= $action ?>" method="<?= $method ?>" id="<?= $form_id ?>" class="needs-validation" novalidate>
        <?php if (!empty($tabs)): ?>
            <!-- Navigation Tabs -->
            <ul class="nav nav-tabs nav-bordered mb-3">
                <?php foreach ($tabs as $index => $tab): ?>
                    <li class="nav-item">
                        <a href="#<?= $tab['tab_id'] ?>" data-bs-toggle="tab" aria-expanded="<?= $index === 0 ? 'true' : 'false' ?>" 
                           class="nav-link <?= $index === 0 ? 'active' : '' ?>">
                            <?php if (!empty($tab['icon'])): ?>
                                <i class="<?= $tab['icon'] ?> d-md-none d-block"></i>
                            <?php endif; ?>
                            <span class="d-none d-md-block"><?= $tab['title'] ?></span>
                        </a>
                    </li>
                <?php endforeach; ?>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content">
                <?php foreach ($tabs as $index => $tab): ?>
                    <div class="tab-pane <?= $index === 0 ? 'show active' : '' ?>" id="<?= $tab['tab_id'] ?>">
                        <?php if (!empty($tab['description'])): ?>
                            <div class="row">
                                <div class="col-12">
                                    <h5 class="mb-3 text-uppercase bg-light p-2">
                                        <?php if (!empty($tab['icon'])): ?>
                                            <i class="<?= $tab['icon'] ?> me-1"></i>
                                        <?php endif; ?>
                                        <?= $tab['title'] ?>
                                    </h5>
                                    <?php if (!empty($tab['description'])): ?>
                                        <p class="text-muted"><?= $tab['description'] ?></p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($tab['sub_table'])): ?>
                            <!-- جدول فرعي ديناميكي -->
                            <?php render_sub_table($tab['sub_table'], $tab['tab_id'], $readonly); ?>
                        <?php elseif (!empty($tab['fields'])): ?>
                            <?php render_tab_fields($tab['fields'], $form_data, $readonly); ?>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </form>
    <?php
}

/**
 * عرض جدول فرعي ديناميكي
 */
function render_sub_table($sub_table_config, $tab_id, $readonly = false)
{
    $table_name = $sub_table_config['name'] ?? $tab_id;
    $description = $sub_table_config['description'] ?? '';
    $columns = $sub_table_config['columns'] ?? [];
    $add_button_text = $sub_table_config['add_button_text'] ?? 'إضافة صف جديد';
    $add_button_icon = $sub_table_config['add_button_icon'] ?? 'mdi mdi-plus';
    $js_function = $sub_table_config['js_function'] ?? 'add' . ucfirst($table_name) . 'Row';

    ?>
    <div class="row mb-3">
        <div class="col-12">
            <?php if ($description): ?>
                <p class="text-muted"><?= $description ?></p>
            <?php endif; ?>
            <?php if (!$readonly): ?>
                <button type="button" class="btn btn-primary btn-sm" onclick="<?= $js_function ?>()">
                    <i class="<?= $add_button_icon ?> me-1"></i> <?= $add_button_text ?>
                </button>
            <?php endif; ?>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-bordered" id="<?= $table_name ?>-table">
            <thead class="table-light">
                <tr>
                    <?php foreach ($columns as $column): ?>
                        <th style="width: <?= $column['width'] ?? 'auto' ?>;"><?= $column['title'] ?></th>
                    <?php endforeach; ?>
                    <th style="width: 6%;">حذف</th>
                </tr>
            </thead>
            <tbody id="<?= $table_name ?>-tbody">
                <!-- سيتم إضافة الصفوف هنا بواسطة JavaScript -->
            </tbody>
        </table>
    </div>

    <script>
    let <?= $table_name ?>Index = 0;

    // إضافة صف جديد
    function <?= $js_function ?>() {
        const tbody = document.getElementById('<?= $table_name ?>-tbody');
        const row = document.createElement('tr');
        row.innerHTML = `
            <?php foreach ($columns as $column): ?>
                <td>
                    <?php render_sub_table_column($column, $table_name); ?>
                </td>
            <?php endforeach; ?>
            <td class="text-center">
                <button type="button" class="btn btn-danger btn-sm" onclick="remove<?= ucfirst($table_name) ?>Row(this)"
                        ${<?= $table_name ?>Index === 0 ? 'style="display:none"' : ''}>
                    <i class="mdi mdi-delete"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
        <?= $table_name ?>Index++;
    }

    // حذف صف
    function remove<?= ucfirst($table_name) ?>Row(button) {
        const row = button.closest('tr');
        row.remove();
    }

    // تهيئة الجدول
    document.addEventListener('DOMContentLoaded', function() {
        if (document.getElementById('<?= $table_name ?>-tbody')) {
            <?= $js_function ?>();
        }
    });
    </script>
    <?php
}

/**
 * عرض عمود في الجدول الفرعي
 */
function render_sub_table_column($column, $table_name, $readonly = false)
{
    $type = $column['type'] ?? 'text';
    $name = $column['name'] ?? '';
    $placeholder = $column['placeholder'] ?? '';
    $required = $column['required'] ?? false;
    $options = $column['options'] ?? [];
    $attributes = $column['attributes'] ?? '';

    // إضافة readonly إذا كان في وضع القراءة فقط
    if ($readonly) {
        $attributes .= ' readonly disabled';
    }

    switch ($type) {
        case 'text':
        case 'email':
        case 'tel':
        case 'number':
            echo '<input type="' . $type . '" class="form-control form-control-sm" ';
            echo 'name="' . $table_name . '[${' . $table_name . 'Index}][' . $name . ']" ';
            echo 'placeholder="' . $placeholder . '" ';
            echo ($required ? 'required ' : '') . $attributes . '>';
            break;

        case 'textarea':
            echo '<textarea class="form-control form-control-sm" ';
            echo 'name="' . $table_name . '[${' . $table_name . 'Index}][' . $name . ']" ';
            echo 'rows="2" placeholder="' . $placeholder . '" ';
            echo ($required ? 'required ' : '') . $attributes . '></textarea>';
            break;

        case 'select':
            echo '<select class="form-select form-select-sm" ';
            echo 'name="' . $table_name . '[${' . $table_name . 'Index}][' . $name . ']" ';
            echo ($required ? 'required ' : '') . $attributes . '>';
            if ($placeholder) {
                echo '<option value="">' . $placeholder . '</option>';
            }
            foreach ($options as $value => $text) {
                $selected = isset($column['default']) && $column['default'] == $value ? 'selected' : '';
                echo '<option value="' . $value . '" ' . $selected . '>' . $text . '</option>';
            }
            echo '</select>';
            break;

        case 'radio':
            echo '<div class="form-check">';
            echo '<input class="form-check-input" type="radio" ';
            echo 'name="' . $column['radio_group'] . '" value="${' . $table_name . 'Index}" ';
            echo ($column['default'] ?? false) ? 'checked' : '';
            echo '>';
            echo '</div>';
            break;

        default:
            echo '<input type="text" class="form-control form-control-sm" ';
            echo 'name="' . $table_name . '[${' . $table_name . 'Index}][' . $name . ']" ';
            echo 'placeholder="' . $placeholder . '" ';
            echo ($required ? 'required ' : '') . $attributes . '>';
    }
}

/**
 * عرض حقول التبويب
 */
function render_tab_fields($fields, $form_data, $readonly = false)
{
    $fields_per_row = 2; // عدد الحقول في كل صف
    $field_chunks = array_chunk($fields, $fields_per_row);
    
    foreach ($field_chunks as $chunk) {
        echo '<div class="row">';
        foreach ($chunk as $field) {
            $col_class = 'col-md-' . (12 / count($chunk));
            echo '<div class="' . $col_class . '">';
            render_form_field($field, $form_data, $readonly);
            echo '</div>';
        }
        echo '</div>';
    }
}

/**
 * عرض نموذج عادي بالحقول
 */
function render_standard_form($form_config, $form_data, $module, $entity)
{
    $action = $form_config['action'] ?? base_url($module . '/' . $entity . '/store');
    $method = $form_config['method'] ?? 'POST';
    $form_id = $form_config['id'] ?? ($entity . 'Form');
    $fields = $form_config['fields'] ?? [];
    ?>
    
    <form action="<?= $action ?>" method="<?= $method ?>" id="<?= $form_id ?>" class="needs-validation" novalidate>
        <?php render_tab_fields($fields, $form_data); ?>
    </form>
    <?php
}

/**
 * عرض حقل نموذج واحد
 */
function render_form_field($field, $form_data, $readonly = false)
{
    $name = $field['name'] ?? '';
    $label = $field['label'] ?? '';
    $type = $field['type'] ?? 'text';
    $value = $form_data[$name] ?? $field['default'] ?? $field['value'] ?? '';
    $required = $field['required'] ?? false;
    $placeholder = $field['placeholder'] ?? '';
    $options = $field['options'] ?? [];
    $help = $field['help'] ?? '';
    $class = $field['class'] ?? '';
    $attributes = $field['attributes'] ?? '';

    // إضافة readonly إذا كان في وضع القراءة فقط
    if ($readonly) {
        $attributes .= ' readonly disabled';
        $class .= ' readonly-field';
    }
    
    ?>
    <div class="mb-3">
        <label for="<?= $name ?>" class="form-label">
            <?= $label ?>
            <?php if ($required): ?>
                <span class="text-danger">*</span>
            <?php endif; ?>
        </label>
        
        <?php switch ($type): 
            case 'text':
            case 'email':
            case 'tel':
            case 'url':
            case 'number':
            case 'date':
            case 'time':
            case 'datetime-local': ?>
                <input type="<?= $type ?>" class="form-control <?= $class ?>" id="<?= $name ?>" name="<?= $name ?>" 
                       value="<?= htmlspecialchars($value) ?>" placeholder="<?= $placeholder ?>"
                       <?= $required ? 'required' : '' ?> <?= $attributes ?>>
                <?php break;
            
            case 'password': ?>
                <input type="password" class="form-control <?= $class ?>" id="<?= $name ?>" name="<?= $name ?>" 
                       placeholder="<?= $placeholder ?>" <?= $required ? 'required' : '' ?> <?= $attributes ?>>
                <?php break;
            
            case 'textarea': ?>
                <textarea class="form-control <?= $class ?>" id="<?= $name ?>" name="<?= $name ?>" 
                          rows="<?= $field['rows'] ?? 3 ?>" placeholder="<?= $placeholder ?>"
                          <?= $required ? 'required' : '' ?> <?= $attributes ?>><?= htmlspecialchars($value) ?></textarea>
                <?php break;
            
            case 'select': ?>
                <select class="form-select <?= $class ?>" id="<?= $name ?>" name="<?= $name ?>" 
                        <?= $required ? 'required' : '' ?> <?= $attributes ?>>
                    <?php if ($placeholder): ?>
                        <option value=""><?= $placeholder ?></option>
                    <?php endif; ?>
                    <?php foreach ($options as $option_value => $option_text): ?>
                        <option value="<?= $option_value ?>" <?= $value == $option_value ? 'selected' : '' ?>>
                            <?= $option_text ?>
                        </option>
                    <?php endforeach; ?>
                </select>
                <?php break;
            
            case 'checkbox': ?>
                <div class="form-check">
                    <input type="checkbox" class="form-check-input <?= $class ?>" id="<?= $name ?>" name="<?= $name ?>" 
                           value="1" <?= $value ? 'checked' : '' ?> <?= $attributes ?>>
                    <label class="form-check-label" for="<?= $name ?>">
                        <?= $field['checkbox_label'] ?? $placeholder ?>
                    </label>
                </div>
                <?php break;
            
            case 'radio': ?>
                <?php foreach ($options as $option_value => $option_text): ?>
                    <div class="form-check">
                        <input type="radio" class="form-check-input <?= $class ?>" id="<?= $name ?>_<?= $option_value ?>" 
                               name="<?= $name ?>" value="<?= $option_value ?>" 
                               <?= $value == $option_value ? 'checked' : '' ?> <?= $attributes ?>>
                        <label class="form-check-label" for="<?= $name ?>_<?= $option_value ?>">
                            <?= $option_text ?>
                        </label>
                    </div>
                <?php endforeach; ?>
                <?php break;
            
            default: ?>
                <input type="<?= $type ?>" class="form-control <?= $class ?>" id="<?= $name ?>" name="<?= $name ?>" 
                       value="<?= htmlspecialchars($value) ?>" placeholder="<?= $placeholder ?>"
                       <?= $required ? 'required' : '' ?> <?= $attributes ?>>
        <?php endswitch; ?>
        
        <?php if ($help): ?>
            <div class="form-text"><?= $help ?></div>
        <?php endif; ?>
        
        <?php if ($required): ?>
            <div class="invalid-feedback">
                يرجى إدخال <?= $label ?>
            </div>
        <?php endif; ?>
    </div>
    <?php
}

/**
 * عرض رأس صفحة النموذج مع الأزرار (محسن للنماذج)
 */
function render_form_header($title, $breadcrumb, $actions = [])
{
    ?>
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div class="page-header-box">
                    <!-- الصف الأول: الروابط والأزرار فقط -->
                    <div class="header-top-row">
                        <!-- حاوي الروابط -->
                        <div class="header-breadcrumb-container">
                            <ol class="header-breadcrumb">
                                <?php foreach ($breadcrumb as $item): ?>
                                    <?php if (isset($item['url']) && !empty($item['url']) && !isset($item['active'])): ?>
                                        <li class="header-breadcrumb-item">
                                            <a href="<?= base_url($item['url']) ?>"><?= $item['title'] ?></a>
                                        </li>
                                    <?php else: ?>
                                        <li class="header-breadcrumb-item active"><?= $item['title'] ?></li>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </ol>
                        </div>

                        <!-- حاوي الأزرار -->
                        <?php if (!empty($actions)): ?>
                            <div class="header-actions-container">
                                <div class="header-actions">
                                    <?php foreach ($actions as $action): ?>
                                        <?php if (isset($action['submit']) && $action['submit']): ?>
                                            <!-- زر submit للنموذج -->
                                            <button type="submit" form="<?= $action['form'] ?? 'mainForm' ?>" class="btn btn-<?= $action['type'] ?> btn-sm">
                                                <i class="<?= $action['icon'] ?>"></i>
                                                <span><?= $action['text'] ?></span>
                                            </button>
                                        <?php elseif ($action['type'] === 'primary'): ?>
                                            <a href="<?= base_url($action['url']) ?>" class="btn btn-primary btn-sm">
                                                <i class="<?= $action['icon'] ?>"></i>
                                                <span><?= $action['text'] ?></span>
                                            </a>
                                        <?php elseif ($action['type'] === 'secondary'): ?>
                                            <a href="<?= base_url($action['url']) ?>" class="btn btn-secondary btn-sm">
                                                <i class="<?= $action['icon'] ?>"></i>
                                                <span><?= $action['text'] ?></span>
                                            </a>
                                        <?php elseif ($action['type'] === 'success'): ?>
                                            <a href="<?= base_url($action['url']) ?>" class="btn btn-success btn-sm">
                                                <i class="<?= $action['icon'] ?>"></i>
                                                <span><?= $action['text'] ?></span>
                                            </a>
                                        <?php elseif ($action['type'] === 'info'): ?>
                                            <a href="<?= base_url($action['url']) ?>" class="btn btn-info btn-sm">
                                                <i class="<?= $action['icon'] ?>"></i>
                                                <span><?= $action['text'] ?></span>
                                            </a>
                                        <?php elseif ($action['type'] === 'warning'): ?>
                                            <a href="<?= base_url($action['url']) ?>" class="btn btn-warning btn-sm">
                                                <i class="<?= $action['icon'] ?>"></i>
                                                <span><?= $action['text'] ?></span>
                                            </a>
                                        <?php elseif ($action['type'] === 'danger'): ?>
                                            <a href="<?= base_url($action['url']) ?>" class="btn btn-danger btn-sm">
                                                <i class="<?= $action['icon'] ?>"></i>
                                                <span><?= $action['text'] ?></span>
                                            </a>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php
}

/**
 * عرض سكريبت النماذج
 */
function render_form_scripts($module, $entity)
{
    ?>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('📝 Forms system loaded for <?= $module ?>/<?= $entity ?>');

        // تفعيل التبويبات
        initFormTabs();

        // تفعيل التحقق من صحة النموذج
        initFormValidation();

        // Focus على أول حقل
        focusFirstField();
    });

    // تفعيل التبويبات
    function initFormTabs() {
        const tabLinks = document.querySelectorAll('.nav-link[data-bs-toggle="tab"]');
        const tabPanes = document.querySelectorAll('.tab-pane');

        tabLinks.forEach(function(tabLink) {
            tabLink.addEventListener('click', function(e) {
                e.preventDefault();

                // إزالة active من جميع التبويبات
                tabLinks.forEach(link => link.classList.remove('active'));
                tabPanes.forEach(pane => {
                    pane.classList.remove('show', 'active');
                });

                // تفعيل التبويب المحدد
                this.classList.add('active');
                const targetId = this.getAttribute('href').substring(1);
                const targetPane = document.getElementById(targetId);

                if (targetPane) {
                    targetPane.classList.add('show', 'active');
                }
            });
        });
    }

    // تفعيل التحقق من صحة النموذج
    function initFormValidation() {
        const forms = document.querySelectorAll('.needs-validation');

        forms.forEach(function(form) {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();

                    // العثور على أول حقل غير صحيح والانتقال إلى تبويبه
                    const firstInvalidField = form.querySelector(':invalid');
                    if (firstInvalidField) {
                        const tabPane = firstInvalidField.closest('.tab-pane');
                        if (tabPane) {
                            const tabId = tabPane.getAttribute('id');
                            const tabLink = document.querySelector(`[href="#${tabId}"]`);
                            if (tabLink) {
                                tabLink.click();
                            }
                        }
                        firstInvalidField.focus();
                    }
                }

                form.classList.add('was-validated');
            });
        });
    }

    // Focus على أول حقل
    function focusFirstField() {
        const firstInput = document.querySelector('.tab-pane.active .form-control:not([readonly]):not([disabled])');
        if (firstInput) {
            setTimeout(() => firstInput.focus(), 100);
        }
    }
    </script>
    <?php
}
?>
