<?php
/**
 * Forms Helper Functions
 * مساعد النماذج - نظام منفصل ومرن للنماذج
 */

/**
 * عرض صفحة نموذج موحدة
 *
 * @param array $config إعدادات الصفحة والنموذج
 * @return void
 */
function render_form_page($config)
{
    // إعداد المتغيرات الأساسية
    $title = $config['title'] ?? 'نموذج البيانات';
    $module = $config['module'] ?? 'default';
    $entity = $config['entity'] ?? 'items';
    
    // بيانات النموذج
    $form_config = $config['form'] ?? [];
    $form_data = $config['data'] ?? [];
    $actions = $config['actions'] ?? [];
    
    // Breadcrumb
    $breadcrumb = $config['breadcrumb'] ?? [
        ['title' => $title, 'active' => true]
    ];

    // عرض الصفحة
    render_form_layout($title, $module, $entity, $form_config, $form_data, $breadcrumb, $actions);
}

/**
 * عرض تخطيط صفحة النموذج
 */
function render_form_layout($title, $module, $entity, $form_config, $form_data, $breadcrumb, $actions)
{
    ?>
    <div class="container-fluid">
        <!-- Header -->
        <?php render_form_header($title, $breadcrumb); ?>

        <!-- Form Content -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <?php render_form_content($form_config, $form_data, $module, $entity); ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <?php if (!empty($actions)): ?>
            <?php render_form_actions($actions); ?>
        <?php endif; ?>
    </div>

    <!-- JavaScript -->
    <?php render_form_scripts($module, $entity); ?>
    <?php
}

/**
 * عرض رأس صفحة النموذج
 */
function render_form_header($title, $breadcrumb)
{
    ?>
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <?php foreach ($breadcrumb as $item): ?>
                            <?php if (isset($item['url']) && !empty($item['url']) && !isset($item['active'])): ?>
                                <li class="breadcrumb-item"><a href="<?= base_url($item['url']) ?>"><?= $item['title'] ?></a></li>
                            <?php else: ?>
                                <li class="breadcrumb-item active"><?= $item['title'] ?></li>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </ol>
                </div>
                <h4 class="page-title"><?= $title ?></h4>
            </div>
        </div>
    </div>
    <?php
}

/**
 * عرض محتوى النموذج
 */
function render_form_content($form_config, $form_data, $module, $entity)
{
    $action = $form_config['action'] ?? base_url($module . '/' . $entity . '/store');
    $method = $form_config['method'] ?? 'POST';
    ?>
    <form action="<?= $action ?>" method="<?= $method ?>" class="form-horizontal">
        <?php if (!empty($form_config['fields'])): ?>
            <?php foreach ($form_config['fields'] as $field): ?>
                <?php render_form_field($field, $form_data); ?>
            <?php endforeach; ?>
        <?php endif; ?>
        
        <div class="form-group row">
            <div class="col-sm-10 offset-sm-2">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ
                </button>
                <a href="<?= base_url($module . '/' . $entity) ?>" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
            </div>
        </div>
    </form>
    <?php
}

/**
 * عرض حقل النموذج
 */
function render_form_field($field, $form_data)
{
    $name = $field['name'] ?? '';
    $label = $field['label'] ?? '';
    $type = $field['type'] ?? 'text';
    $value = $form_data[$name] ?? $field['value'] ?? '';
    $required = $field['required'] ?? false;
    $placeholder = $field['placeholder'] ?? '';
    $options = $field['options'] ?? [];
    
    ?>
    <div class="form-group row">
        <label for="<?= $name ?>" class="col-sm-2 col-form-label">
            <?= $label ?>
            <?php if ($required): ?>
                <span class="text-danger">*</span>
            <?php endif; ?>
        </label>
        <div class="col-sm-10">
            <?php switch ($type): 
                case 'text': ?>
                    <input type="text" class="form-control" id="<?= $name ?>" name="<?= $name ?>" 
                           value="<?= htmlspecialchars($value) ?>" placeholder="<?= $placeholder ?>"
                           <?= $required ? 'required' : '' ?>>
                    <?php break;
                
                case 'email': ?>
                    <input type="email" class="form-control" id="<?= $name ?>" name="<?= $name ?>" 
                           value="<?= htmlspecialchars($value) ?>" placeholder="<?= $placeholder ?>"
                           <?= $required ? 'required' : '' ?>>
                    <?php break;
                
                case 'password': ?>
                    <input type="password" class="form-control" id="<?= $name ?>" name="<?= $name ?>" 
                           placeholder="<?= $placeholder ?>" <?= $required ? 'required' : '' ?>>
                    <?php break;
                
                case 'textarea': ?>
                    <textarea class="form-control" id="<?= $name ?>" name="<?= $name ?>" 
                              rows="<?= $field['rows'] ?? 3 ?>" placeholder="<?= $placeholder ?>"
                              <?= $required ? 'required' : '' ?>><?= htmlspecialchars($value) ?></textarea>
                    <?php break;
                
                case 'select': ?>
                    <select class="form-control" id="<?= $name ?>" name="<?= $name ?>" <?= $required ? 'required' : '' ?>>
                        <?php if ($placeholder): ?>
                            <option value=""><?= $placeholder ?></option>
                        <?php endif; ?>
                        <?php foreach ($options as $option_value => $option_text): ?>
                            <option value="<?= $option_value ?>" <?= $value == $option_value ? 'selected' : '' ?>>
                                <?= $option_text ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <?php break;
                
                case 'checkbox': ?>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="<?= $name ?>" name="<?= $name ?>" 
                               value="1" <?= $value ? 'checked' : '' ?>>
                        <label class="form-check-label" for="<?= $name ?>">
                            <?= $field['checkbox_label'] ?? $label ?>
                        </label>
                    </div>
                    <?php break;
                
                default: ?>
                    <input type="<?= $type ?>" class="form-control" id="<?= $name ?>" name="<?= $name ?>" 
                           value="<?= htmlspecialchars($value) ?>" placeholder="<?= $placeholder ?>"
                           <?= $required ? 'required' : '' ?>>
            <?php endswitch; ?>
            
            <?php if (isset($field['help'])): ?>
                <small class="form-text text-muted"><?= $field['help'] ?></small>
            <?php endif; ?>
        </div>
    </div>
    <?php
}

/**
 * عرض إجراءات النموذج
 */
function render_form_actions($actions)
{
    ?>
    <div class="row mt-3">
        <div class="col-12">
            <div class="form-actions text-center">
                <?php foreach ($actions as $action): ?>
                    <a href="<?= base_url($action['url']) ?>" class="btn btn-<?= $action['type'] ?? 'secondary' ?>">
                        <i class="<?= $action['icon'] ?>"></i>
                        <?= $action['text'] ?>
                    </a>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    <?php
}

/**
 * عرض JavaScript للنماذج
 */
function render_form_scripts($module, $entity)
{
    ?>
    <script>
    // Forms JavaScript
    document.addEventListener('DOMContentLoaded', function() {
        console.log('📝 Forms system loaded for <?= $module ?>/<?= $entity ?>');
        
        // يمكن إضافة تفاعلات للنماذج هنا
        // مثل: validation، auto-save، إلخ
    });
    </script>
    <?php
}
?>
