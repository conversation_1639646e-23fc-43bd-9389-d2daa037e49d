<?php
/**
 * Forms Helper Functions
 * مساعد النماذج - نظام منفصل ومتخصص
 *
 * هذا الملف مخصص للنماذج فقط
 * يتبع نفس نمط datatable_helper.php
 */

/**
 * عرض صفحة نموذج متخصصة
 * هذه الدالة مخصصة للنماذج فقط - تتبع نفس نمط render_datatable_page
 *
 * @param array $config إعدادات النموذج
 * @return void
 */
function render_form_page($config)
{
    // إعداد المتغيرات الأساسية للنماذج
    $title = $config['title'] ?? 'نموذج البيانات';
    $module = $config['module'] ?? 'default';
    $entity = $config['entity'] ?? 'items';

    // بيانات النموذج (مطلوبة)
    $form_config = $config['form'] ?? [];
    $form_data = $config['form_data'] ?? [];

    // إعدادات اختيارية
    $actions = $config['actions'] ?? []; // الأزرار تأتي من ملف العرض
    $custom_content = $config['custom_content'] ?? null; // محتوى مخصص

    // Breadcrumb
    $breadcrumb = $config['breadcrumb'] ?? [
        ['title' => $title, 'active' => true]
    ];

    // عرض النموذج فقط - نفس طريقة datatable_helper
    render_form_layout($title, $module, $entity, $form_config, $form_data, $breadcrumb, $actions, $custom_content);
}

/**
 * عرض تخطيط النموذج المتخصص
 * مخصص للنماذج فقط - نفس نظام datatable_helper
 */
function render_form_layout($title, $module, $entity, $form_config, $form_data, $breadcrumb, $actions, $custom_content)
{
    ?>
    <div class="container-fluid">
        <!-- Header with Actions - نفس نظام datatable_helper -->
        <?php render_datatable_header($title, $breadcrumb, $actions); ?>

        <!-- Form Content -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <?php if ($custom_content && file_exists($custom_content)): ?>
                            <!-- محتوى مخصص للنماذج المعقدة -->
                            <?php include $custom_content; ?>
                        <?php elseif (!empty($form_config['tabs'])): ?>
                            <!-- نموذج بتبويبات ديناميكية -->
                            <?php render_tabbed_form($form_config, $form_data, $module, $entity); ?>
                        <?php elseif (!empty($form_config['fields'])): ?>
                            <!-- نموذج عادي بالحقول -->
                            <?php render_standard_form($form_config, $form_data, $module, $entity); ?>
                        <?php else: ?>
                            <!-- رسالة خطأ -->
                            <div class="alert alert-warning">
                                <i class="mdi mdi-alert-circle me-2"></i>
                                لم يتم تحديد محتوى النموذج أو الحقول المطلوبة.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <?php render_form_scripts($module, $entity); ?>
    <?php
}

/**
 * عرض نموذج بتبويبات ديناميكية
 */
function render_tabbed_form($form_config, $form_data, $module, $entity)
{
    $action = $form_config['action'] ?? base_url($module . '/' . $entity . '/store');
    $method = $form_config['method'] ?? 'POST';
    $form_id = $form_config['id'] ?? ($entity . 'Form');
    $tabs = $form_config['tabs'] ?? [];
    ?>
    
    <form action="<?= $action ?>" method="<?= $method ?>" id="<?= $form_id ?>" class="needs-validation" novalidate>
        <?php if (!empty($tabs)): ?>
            <!-- Navigation Tabs -->
            <ul class="nav nav-tabs nav-bordered mb-3">
                <?php foreach ($tabs as $index => $tab): ?>
                    <li class="nav-item">
                        <a href="#<?= $tab['tab_id'] ?>" data-bs-toggle="tab" aria-expanded="<?= $index === 0 ? 'true' : 'false' ?>" 
                           class="nav-link <?= $index === 0 ? 'active' : '' ?>">
                            <?php if (!empty($tab['icon'])): ?>
                                <i class="<?= $tab['icon'] ?> d-md-none d-block"></i>
                            <?php endif; ?>
                            <span class="d-none d-md-block"><?= $tab['title'] ?></span>
                        </a>
                    </li>
                <?php endforeach; ?>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content">
                <?php foreach ($tabs as $index => $tab): ?>
                    <div class="tab-pane <?= $index === 0 ? 'show active' : '' ?>" id="<?= $tab['tab_id'] ?>">
                        <?php if (!empty($tab['description'])): ?>
                            <div class="row">
                                <div class="col-12">
                                    <h5 class="mb-3 text-uppercase bg-light p-2">
                                        <?php if (!empty($tab['icon'])): ?>
                                            <i class="<?= $tab['icon'] ?> me-1"></i>
                                        <?php endif; ?>
                                        <?= $tab['title'] ?>
                                    </h5>
                                    <?php if (!empty($tab['description'])): ?>
                                        <p class="text-muted"><?= $tab['description'] ?></p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($tab['fields'])): ?>
                            <?php render_tab_fields($tab['fields'], $form_data); ?>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </form>
    <?php
}

/**
 * عرض حقول التبويب
 */
function render_tab_fields($fields, $form_data)
{
    $fields_per_row = 2; // عدد الحقول في كل صف
    $field_chunks = array_chunk($fields, $fields_per_row);
    
    foreach ($field_chunks as $chunk) {
        echo '<div class="row">';
        foreach ($chunk as $field) {
            $col_class = 'col-md-' . (12 / count($chunk));
            echo '<div class="' . $col_class . '">';
            render_form_field($field, $form_data);
            echo '</div>';
        }
        echo '</div>';
    }
}

/**
 * عرض نموذج عادي بالحقول
 */
function render_standard_form($form_config, $form_data, $module, $entity)
{
    $action = $form_config['action'] ?? base_url($module . '/' . $entity . '/store');
    $method = $form_config['method'] ?? 'POST';
    $form_id = $form_config['id'] ?? ($entity . 'Form');
    $fields = $form_config['fields'] ?? [];
    ?>
    
    <form action="<?= $action ?>" method="<?= $method ?>" id="<?= $form_id ?>" class="needs-validation" novalidate>
        <?php render_tab_fields($fields, $form_data); ?>
    </form>
    <?php
}

/**
 * عرض حقل نموذج واحد
 */
function render_form_field($field, $form_data)
{
    $name = $field['name'] ?? '';
    $label = $field['label'] ?? '';
    $type = $field['type'] ?? 'text';
    $value = $form_data[$name] ?? $field['default'] ?? $field['value'] ?? '';
    $required = $field['required'] ?? false;
    $placeholder = $field['placeholder'] ?? '';
    $options = $field['options'] ?? [];
    $help = $field['help'] ?? '';
    $class = $field['class'] ?? '';
    $attributes = $field['attributes'] ?? '';
    
    ?>
    <div class="mb-3">
        <label for="<?= $name ?>" class="form-label">
            <?= $label ?>
            <?php if ($required): ?>
                <span class="text-danger">*</span>
            <?php endif; ?>
        </label>
        
        <?php switch ($type): 
            case 'text':
            case 'email':
            case 'tel':
            case 'url':
            case 'number':
            case 'date':
            case 'time':
            case 'datetime-local': ?>
                <input type="<?= $type ?>" class="form-control <?= $class ?>" id="<?= $name ?>" name="<?= $name ?>" 
                       value="<?= htmlspecialchars($value) ?>" placeholder="<?= $placeholder ?>"
                       <?= $required ? 'required' : '' ?> <?= $attributes ?>>
                <?php break;
            
            case 'password': ?>
                <input type="password" class="form-control <?= $class ?>" id="<?= $name ?>" name="<?= $name ?>" 
                       placeholder="<?= $placeholder ?>" <?= $required ? 'required' : '' ?> <?= $attributes ?>>
                <?php break;
            
            case 'textarea': ?>
                <textarea class="form-control <?= $class ?>" id="<?= $name ?>" name="<?= $name ?>" 
                          rows="<?= $field['rows'] ?? 3 ?>" placeholder="<?= $placeholder ?>"
                          <?= $required ? 'required' : '' ?> <?= $attributes ?>><?= htmlspecialchars($value) ?></textarea>
                <?php break;
            
            case 'select': ?>
                <select class="form-select <?= $class ?>" id="<?= $name ?>" name="<?= $name ?>" 
                        <?= $required ? 'required' : '' ?> <?= $attributes ?>>
                    <?php if ($placeholder): ?>
                        <option value=""><?= $placeholder ?></option>
                    <?php endif; ?>
                    <?php foreach ($options as $option_value => $option_text): ?>
                        <option value="<?= $option_value ?>" <?= $value == $option_value ? 'selected' : '' ?>>
                            <?= $option_text ?>
                        </option>
                    <?php endforeach; ?>
                </select>
                <?php break;
            
            case 'checkbox': ?>
                <div class="form-check">
                    <input type="checkbox" class="form-check-input <?= $class ?>" id="<?= $name ?>" name="<?= $name ?>" 
                           value="1" <?= $value ? 'checked' : '' ?> <?= $attributes ?>>
                    <label class="form-check-label" for="<?= $name ?>">
                        <?= $field['checkbox_label'] ?? $placeholder ?>
                    </label>
                </div>
                <?php break;
            
            case 'radio': ?>
                <?php foreach ($options as $option_value => $option_text): ?>
                    <div class="form-check">
                        <input type="radio" class="form-check-input <?= $class ?>" id="<?= $name ?>_<?= $option_value ?>" 
                               name="<?= $name ?>" value="<?= $option_value ?>" 
                               <?= $value == $option_value ? 'checked' : '' ?> <?= $attributes ?>>
                        <label class="form-check-label" for="<?= $name ?>_<?= $option_value ?>">
                            <?= $option_text ?>
                        </label>
                    </div>
                <?php endforeach; ?>
                <?php break;
            
            default: ?>
                <input type="<?= $type ?>" class="form-control <?= $class ?>" id="<?= $name ?>" name="<?= $name ?>" 
                       value="<?= htmlspecialchars($value) ?>" placeholder="<?= $placeholder ?>"
                       <?= $required ? 'required' : '' ?> <?= $attributes ?>>
        <?php endswitch; ?>
        
        <?php if ($help): ?>
            <div class="form-text"><?= $help ?></div>
        <?php endif; ?>
        
        <?php if ($required): ?>
            <div class="invalid-feedback">
                يرجى إدخال <?= $label ?>
            </div>
        <?php endif; ?>
    </div>
    <?php
}

/**
 * عرض سكريبت النماذج
 */
function render_form_scripts($module, $entity)
{
    ?>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('📝 Forms system loaded for <?= $module ?>/<?= $entity ?>');
        
        // يمكن إضافة تفاعلات للنماذج هنا
        // مثل: validation، auto-save، إلخ
    });
    </script>
    <?php
}
?>
