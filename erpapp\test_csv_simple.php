<?php
/**
 * اختبار مبسط لنظام CSV
 */

require_once 'config/config.php';

echo "<h2>🧪 اختبار مبسط لنظام CSV</h2>";

// إنشاء ملف CSV تجريبي للموردين
$csv_content = "اسم المورد,اسم الشركة,الهاتف,البريد الإلكتروني\n";
$csv_content .= "شركة الأمثال,شركة الأمثال المحدودة,0112345678,<EMAIL>\n";
$csv_content .= "مؤسسة التجارة,مؤسسة التجارة العامة,0501234567,<EMAIL>\n";
$csv_content .= "شركة الخدمات,شركة الخدمات المتقدمة,0551234567,<EMAIL>\n";

// حفظ الملف
$test_file = BASE_PATH . '/storage/test_suppliers.csv';
if (!is_dir(dirname($test_file))) {
    mkdir(dirname($test_file), 0755, true);
}

file_put_contents($test_file, "\xEF\xBB\xBF" . $csv_content);

echo "<h3>✅ تم إنشاء ملف CSV تجريبي</h3>";
echo "<p><strong>المسار:</strong> " . $test_file . "</p>";
echo "<p><strong>الحجم:</strong> " . filesize($test_file) . " بايت</p>";

echo "<h4>محتوى الملف:</h4>";
echo "<pre>" . htmlspecialchars($csv_content) . "</pre>";

echo "<h3>🔗 روابط الاختبار</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<h4>1. اختبار صفحة الموردين:</h4>";
echo "<p><a href='index.php#/purchases/suppliers' target='_blank' style='background: #007bff; color: white; padding: 8px 15px; text-decoration: none; border-radius: 3px;'>🔵 صفحة الموردين</a></p>";

echo "<h4>2. اختبار صفحة مجموعات الموردين:</h4>";
echo "<p><a href='index.php#/purchases/supplier-groups' target='_blank' style='background: #28a745; color: white; padding: 8px 15px; text-decoration: none; border-radius: 3px;'>🟢 صفحة مجموعات الموردين</a></p>";

echo "<h4>3. اختبار تشخيص المشاكل:</h4>";
echo "<p><a href='debug_csv_import.php' target='_blank' style='background: #ffc107; color: black; padding: 8px 15px; text-decoration: none; border-radius: 3px;'>🟡 تشخيص المشاكل</a></p>";

echo "<h4>4. اختبار مشكلة الأعمدة:</h4>";
echo "<p><a href='test_csv_headers.php' target='_blank' style='background: #dc3545; color: white; padding: 8px 15px; text-decoration: none; border-radius: 3px;'>🔴 اختبار الأعمدة</a></p>";

echo "<h4>5. تنزيل ملفات تجريبية:</h4>";
echo "<p><a href='storage/test_suppliers.csv' download style='background: #6c757d; color: white; padding: 8px 15px; text-decoration: none; border-radius: 3px; margin-right: 10px;'>⬇️ ملف CSV (فاصلة منقوطة)</a>";
echo "<a href='storage/test_suppliers_comma.csv' download style='background: #28a745; color: white; padding: 8px 15px; text-decoration: none; border-radius: 3px;'>⬇️ ملف CSV (فاصلة عادية)</a></p>";
echo "</div>";

echo "<h3>📋 خطوات الاختبار</h3>";
echo "<ol>";
echo "<li><strong>اذهب إلى صفحة الموردين</strong></li>";
echo "<li><strong>اضغط على زر 'استيراد CSV'</strong> (الزر الأصفر)</li>";
echo "<li><strong>ارفع الملف التجريبي</strong> الذي تم إنشاؤه أعلاه</li>";
echo "<li><strong>تحقق من عرض الأعمدة</strong> - يجب أن تظهر كل عمود في صف منفصل</li>";
echo "<li><strong>اختر التطابق</strong> بين أعمدة الملف وحقول النظام</li>";
echo "<li><strong>اضغط معاينة</strong> لرؤية البيانات قبل الاستيراد</li>";
echo "<li><strong>اضغط استيراد</strong> لتنفيذ الاستيراد</li>";
echo "</ol>";

echo "<h3>🔍 ما يجب ملاحظته</h3>";
echo "<div style='background: #e7f3ff; padding: 15px; border-left: 4px solid #007bff;'>";
echo "<h4>المشكلة الأولى: عرض الأعمدة</h4>";
echo "<p>يجب أن تظهر الأعمدة هكذا:</p>";
echo "<ul>";
echo "<li>✅ اسم المورد</li>";
echo "<li>✅ اسم الشركة</li>";
echo "<li>✅ الهاتف</li>";
echo "<li>✅ البريد الإلكتروني</li>";
echo "</ul>";
echo "<p><strong>وليس هكذا:</strong> اسم المورد,اسم الشركة,الهاتف,البريد الإلكتروني</p>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin-top: 10px;'>";
echo "<h4>المشكلة الثانية: خطأ group_id</h4>";
echo "<p>إذا ظهر خطأ 'Undefined array key group_id'، فهذا يعني:</p>";
echo "<ul>";
echo "<li>🔍 النظام يبحث عن حقل group_id ولكنه غير موجود في الملف</li>";
echo "<li>🔧 تم إضافة هذا الحقل إلى إعدادات الاستيراد كحقل اختياري</li>";
echo "<li>✅ يجب أن يعمل الآن بدون مشاكل</li>";
echo "</ul>";
echo "</div>";

echo "<h3>🛠️ أدوات التشخيص</h3>";
echo "<p>إذا واجهت مشاكل، استخدم هذه الأدوات:</p>";
echo "<ul>";
echo "<li><strong>F12</strong> - افتح أدوات المطور في المتصفح</li>";
echo "<li><strong>Console</strong> - راقب رسائل console.log</li>";
echo "<li><strong>Network</strong> - تحقق من طلبات AJAX</li>";
echo "<li><strong>ملف التشخيص</strong> - استخدم debug_csv_import.php</li>";
echo "</ul>";

echo "<h3>📞 الدعم</h3>";
echo "<p>إذا استمرت المشاكل، أرسل لي:</p>";
echo "<ol>";
echo "<li>لقطة شاشة من صفحة تطابق الأعمدة</li>";
echo "<li>رسائل الخطأ من Console (F12)</li>";
echo "<li>محتوى ملف CSV الذي تحاول استيراده</li>";
echo "</ol>";

echo "<hr>";
echo "<p style='text-align: center; color: #666;'>";
echo "🚀 <strong>نظام CSV جاهز للاختبار!</strong><br>";
echo "تم إصلاح مشاكل عرض الأعمدة وخطأ group_id";
echo "</p>";
?>
