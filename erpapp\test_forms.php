<?php
/**
 * اختبار سريع للنماذج الديناميكية
 */

// تحميل النظام
require_once 'loader.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>اختبار النماذج</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";

echo "<div class='container mt-5'>";
echo "<h1>اختبار النماذج الديناميكية</h1>";

// التحقق من تحميل الدوال
echo "<div class='alert alert-info'>";
echo "<h4>حالة النظام:</h4>";
echo "<ul>";

if (function_exists('render_form_page')) {
    echo "<li style='color: green;'>✅ دالة render_form_page محملة</li>";
} else {
    echo "<li style='color: red;'>❌ دالة render_form_page غير محملة</li>";
}

if (function_exists('render_datatable_header')) {
    echo "<li style='color: green;'>✅ دالة render_datatable_header محملة</li>";
} else {
    echo "<li style='color: red;'>❌ دالة render_datatable_header غير محملة</li>";
}

if (function_exists('render_tabbed_form')) {
    echo "<li style='color: green;'>✅ دالة render_tabbed_form محملة</li>";
} else {
    echo "<li style='color: red;'>❌ دالة render_tabbed_form غير محملة</li>";
}

echo "</ul>";
echo "</div>";

// اختبار النموذج البسيط
if (function_exists('render_form_page')) {
    echo "<hr>";
    echo "<h2>اختبار النموذج:</h2>";
    
    $form_tabs = [
        [
            'tab_id' => 'basic',
            'title' => 'البيانات الأساسية',
            'fields' => [
                [
                    'name' => 'name',
                    'label' => 'الاسم',
                    'type' => 'text',
                    'required' => true
                ],
                [
                    'name' => 'email',
                    'label' => 'البريد الإلكتروني',
                    'type' => 'email'
                ]
            ]
        ]
    ];
    
    try {
        render_form_page([
            'title' => 'نموذج اختبار',
            'module' => 'test',
            'entity' => 'items',
            'breadcrumb' => [
                ['title' => 'اختبار', 'active' => true]
            ],
            'actions' => [
                [
                    'type' => 'primary',
                    'form' => 'testForm',
                    'text' => 'حفظ',
                    'submit' => true
                ]
            ],
            'form' => [
                'id' => 'testForm',
                'tabs' => $form_tabs
            ]
        ]);
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>خطأ: " . $e->getMessage() . "</div>";
    }
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
