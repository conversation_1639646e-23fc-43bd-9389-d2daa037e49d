<!DOCTYPE html>
<html>
<head>
    <title>إنشاء الأصوات</title>
</head>
<body>
    <h1>إنشاء ملفات الأصوات</h1>
    <button onclick="createSounds()">إنشاء الأصوات</button>
    
    <script>
    function createSounds() {
        // إنشاء أصوات بسيطة باستخدام Web Audio API
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        
        const sounds = {
            'success': { freq: 800, duration: 0.3 },
            'create': { freq: 600, duration: 0.4 },
            'update': { freq: 700, duration: 0.3 },
            'delete': { freq: 400, duration: 0.5 },
            'save': { freq: 650, duration: 0.3 },
            'error': { freq: 300, duration: 0.6 },
            'warning': { freq: 500, duration: 0.4 },
            'info': { freq: 750, duration: 0.3 },
            'default': { freq: 600, duration: 0.3 }
        };
        
        Object.keys(sounds).forEach(name => {
            const sound = sounds[name];
            createTone(audioContext, sound.freq, sound.duration, name);
        });
    }
    
    function createTone(audioContext, frequency, duration, name) {
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.frequency.value = frequency;
        oscillator.type = 'sine';
        
        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + duration);
        
        console.log(`تم إنشاء صوت ${name}`);
    }
    </script>
</body>
</html>
