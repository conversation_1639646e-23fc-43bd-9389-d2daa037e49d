<?php
/**
 * Import Export Helper Functions
 * مساعد الاستيراد والتصدير - نظام متكامل
 */

// التأكد من تحميل مكتبة PhpSpreadsheet
if (!class_exists('PhpOffice\PhpSpreadsheet\Spreadsheet')) {
    // محاولة تحميل autoloader
    $autoload_path = '/home1/qqoshqmy/public_html/vendor/autoload.php';
    if (file_exists($autoload_path)) {
        require_once $autoload_path;
    }
}

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Font;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;

/**
 * التحقق من تحميل مكتبة PhpSpreadsheet
 */
function check_phpspreadsheet_loaded()
{
    if (!class_exists('PhpOffice\PhpSpreadsheet\Spreadsheet')) {
        // محاولة أخيرة لتحميل المكتبة
        $autoload_path = '/home1/qqoshqmy/public_html/vendor/autoload.php';

        if (file_exists($autoload_path)) {
            require_once $autoload_path;
        }

        if (!class_exists('PhpOffice\PhpSpreadsheet\Spreadsheet')) {
            throw new Exception('مكتبة PhpSpreadsheet غير متوفرة. يرجى التأكد من تثبيت المكتبة باستخدام Composer.');
        }
    }
}

/**
 * تصدير البيانات إلى Excel
 */
function export_data_to_excel($data, $columns, $config = [])
{
    try {
        // التحقق من تحميل المكتبة
        check_phpspreadsheet_loaded();
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        
        // إعدادات افتراضية
        $title = $config['title'] ?? 'تصدير البيانات';
        $filename = $config['filename'] ?? 'export_' . date('Y-m-d_H-i-s');
        $include_filters = $config['include_filters'] ?? false;
        $filters = $config['filters'] ?? [];
        
        // إعداد معلومات الملف
        $spreadsheet->getProperties()
            ->setCreator('ERP System')
            ->setTitle($title)
            ->setDescription('تصدير البيانات من نظام ERP')
            ->setCreated(time());
        
        $currentRow = 1;
        
        // إضافة عنوان الملف
        if (!empty($title)) {
            $sheet->setCellValue('A1', $title);
            $sheet->mergeCells('A1:' . chr(64 + count($columns)) . '1');
            
            // تنسيق العنوان
            $sheet->getStyle('A1')->applyFromArray([
                'font' => [
                    'bold' => true,
                    'size' => 16,
                    'color' => ['rgb' => '2563eb']
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'vertical' => Alignment::VERTICAL_CENTER
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'f1f5f9']
                ]
            ]);
            
            $sheet->getRowDimension(1)->setRowHeight(30);
            $currentRow = 3;
        }
        
        // إضافة معلومات الفلاتر
        if ($include_filters && !empty($filters)) {
            $sheet->setCellValue('A' . $currentRow, 'الفلاتر المطبقة:');
            $sheet->getStyle('A' . $currentRow)->getFont()->setBold(true);
            $currentRow++;
            
            foreach ($filters as $key => $value) {
                if (!empty($value)) {
                    $filterLabel = get_filter_label($key);
                    $sheet->setCellValue('A' . $currentRow, $filterLabel . ': ' . $value);
                    $currentRow++;
                }
            }
            $currentRow++; // سطر فارغ
        }
        
        // إضافة رؤوس الأعمدة
        $col = 'A';
        foreach ($columns as $column) {
            if (isset($column['export']) && $column['export'] === false) {
                continue; // تخطي الأعمدة المستبعدة من التصدير
            }
            
            $sheet->setCellValue($col . $currentRow, $column['title']);
            $col++;
        }
        
        // تنسيق رؤوس الأعمدة
        $headerRange = 'A' . $currentRow . ':' . chr(64 + count($columns)) . $currentRow;
        $sheet->getStyle($headerRange)->applyFromArray([
            'font' => [
                'bold' => true,
                'color' => ['rgb' => 'ffffff']
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => '1e40af']
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000']
                ]
            ]
        ]);
        
        $currentRow++;
        
        // إضافة البيانات
        foreach ($data as $row) {
            $col = 'A';
            foreach ($columns as $column) {
                if (isset($column['export']) && $column['export'] === false) {
                    continue;
                }
                
                $field = $column['field'];
                $value = $row[$field] ?? '';
                
                // معالجة أنواع البيانات المختلفة
                $value = format_export_value($value, $column);
                
                $sheet->setCellValue($col . $currentRow, $value);
                $col++;
            }
            $currentRow++;
        }
        
        // تنسيق البيانات
        if ($currentRow > 1) {
            $dataRange = 'A' . ($currentRow - count($data)) . ':' . chr(64 + count($columns)) . ($currentRow - 1);
            $sheet->getStyle($dataRange)->applyFromArray([
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => 'cccccc']
                    ]
                ],
                'alignment' => [
                    'vertical' => Alignment::VERTICAL_CENTER
                ]
            ]);
        }
        
        // ضبط عرض الأعمدة تلقائياً
        foreach (range('A', chr(64 + count($columns))) as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }
        
        // إضافة معلومات التصدير في النهاية
        $currentRow += 2;
        $sheet->setCellValue('A' . $currentRow, 'تم التصدير في: ' . date('Y-m-d H:i:s'));
        $sheet->setCellValue('A' . ($currentRow + 1), 'عدد السجلات: ' . count($data));
        
        // حفظ الملف
        $writer = new Xlsx($spreadsheet);
        $filepath = sys_get_temp_dir() . '/' . $filename . '.xlsx';
        $writer->save($filepath);
        
        return [
            'success' => true,
            'filepath' => $filepath,
            'filename' => $filename . '.xlsx',
            'records_count' => count($data)
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * تنسيق قيمة للتصدير
 */
function format_export_value($value, $column)
{
    $type = $column['type'] ?? 'text';
    
    switch ($type) {
        case 'badge':
            // إذا كان هناك تكوين للحالة، استخدم النص المناسب
            if (isset($column['status_config']['texts'][$value])) {
                return $column['status_config']['texts'][$value];
            }
            return $value;
            
        case 'actions':
            return ''; // لا نصدر أعمدة الإجراءات
            
        case 'link':
            return $value; // نصدر النص فقط بدون الرابط
            
        default:
            return $value;
    }
}

/**
 * الحصول على تسمية الفلتر
 */
function get_filter_label($key)
{
    $labels = [
        'search' => 'البحث',
        'status' => 'الحالة',
        'group_id' => 'المجموعة',
        'per_page' => 'عدد العناصر لكل صفحة',
        'current_page' => 'الصفحة الحالية'
    ];
    
    return $labels[$key] ?? $key;
}

/**
 * قراءة ملف Excel وإرجاع البيانات
 */
function read_excel_file($filepath)
{
    try {
        // التحقق من تحميل المكتبة
        check_phpspreadsheet_loaded();
        $spreadsheet = IOFactory::load($filepath);
        $sheet = $spreadsheet->getActiveSheet();
        $data = $sheet->toArray();
        
        // إزالة الصفوف الفارغة
        $data = array_filter($data, function($row) {
            return !empty(array_filter($row));
        });
        
        return [
            'success' => true,
            'data' => array_values($data), // إعادة ترقيم المفاتيح
            'headers' => !empty($data) ? array_shift($data) : [],
            'rows_count' => count($data)
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * إنشاء قالب للاستيراد
 */
function create_import_template($columns, $config = [])
{
    try {
        // التحقق من تحميل المكتبة
        check_phpspreadsheet_loaded();
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        
        $title = $config['title'] ?? 'قالب الاستيراد';
        $filename = $config['filename'] ?? 'import_template_' . date('Y-m-d_H-i-s');
        
        // إعداد معلومات الملف
        $spreadsheet->getProperties()
            ->setCreator('ERP System')
            ->setTitle($title)
            ->setDescription('قالب استيراد البيانات');
        
        $currentRow = 1;
        
        // إضافة عنوان القالب
        $sheet->setCellValue('A1', $title);
        $sheet->mergeCells('A1:' . chr(64 + count($columns)) . '1');
        
        // تنسيق العنوان
        $sheet->getStyle('A1')->applyFromArray([
            'font' => [
                'bold' => true,
                'size' => 16,
                'color' => ['rgb' => '059669']
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'ecfdf5']
            ]
        ]);
        
        $currentRow = 3;
        
        // إضافة رؤوس الأعمدة
        $col = 'A';
        foreach ($columns as $column) {
            if (isset($column['import']) && $column['import'] === false) {
                continue; // تخطي الأعمدة المستبعدة من الاستيراد
            }
            
            $sheet->setCellValue($col . $currentRow, $column['title']);
            $col++;
        }
        
        // تنسيق رؤوس الأعمدة
        $headerRange = 'A' . $currentRow . ':' . chr(64 + count($columns)) . $currentRow;
        $sheet->getStyle($headerRange)->applyFromArray([
            'font' => [
                'bold' => true,
                'color' => ['rgb' => 'ffffff']
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => '059669']
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000']
                ]
            ]
        ]);
        
        // ضبط عرض الأعمدة
        foreach (range('A', chr(64 + count($columns))) as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }
        
        // حفظ الملف
        $writer = new Xlsx($spreadsheet);
        $filepath = sys_get_temp_dir() . '/' . $filename . '.xlsx';
        $writer->save($filepath);
        
        return [
            'success' => true,
            'filepath' => $filepath,
            'filename' => $filename . '.xlsx'
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * معالجة تطابق الأعمدة للاستيراد
 */
function process_column_mapping($excel_headers, $system_columns, $mapping)
{
    $mapped_data = [];
    $errors = [];

    // التحقق من الأعمدة المطلوبة
    foreach ($system_columns as $column) {
        if (isset($column['required']) && $column['required'] === true) {
            $field = $column['field'];
            if (!isset($mapping[$field]) || $mapping[$field] === '') {
                $errors[] = "العمود المطلوب '{$column['title']}' غير مطابق";
            }
        }
    }

    if (!empty($errors)) {
        return [
            'success' => false,
            'errors' => $errors
        ];
    }

    return [
        'success' => true,
        'mapping' => $mapping
    ];
}

/**
 * استيراد البيانات مع التحقق من الصحة
 */
function import_data_with_validation($excel_data, $mapping, $system_columns, $validation_callback = null)
{
    $imported_data = [];
    $errors = [];
    $row_number = 1;

    foreach ($excel_data as $excel_row) {
        $row_number++;
        $mapped_row = [];
        $row_errors = [];

        // تطبيق التطابق
        foreach ($mapping as $system_field => $excel_column_index) {
            if ($excel_column_index !== '' && isset($excel_row[$excel_column_index])) {
                $value = trim($excel_row[$excel_column_index]);

                // العثور على تكوين العمود
                $column_config = null;
                foreach ($system_columns as $col) {
                    if ($col['field'] === $system_field) {
                        $column_config = $col;
                        break;
                    }
                }

                // تطبيق التحقق من الصحة
                $validation_result = validate_import_value($value, $column_config, $row_number);
                if (!$validation_result['valid']) {
                    $row_errors[] = $validation_result['error'];
                } else {
                    $mapped_row[$system_field] = $validation_result['value'];
                }
            } else {
                // التحقق من الحقول المطلوبة
                $column_config = null;
                foreach ($system_columns as $col) {
                    if ($col['field'] === $system_field) {
                        $column_config = $col;
                        break;
                    }
                }

                if (isset($column_config['required']) && $column_config['required'] === true) {
                    $row_errors[] = "الحقل المطلوب '{$column_config['title']}' فارغ في الصف {$row_number}";
                }
            }
        }

        // تطبيق التحقق المخصص إذا وجد
        if ($validation_callback && is_callable($validation_callback)) {
            $custom_validation = $validation_callback($mapped_row, $row_number);
            if (!$custom_validation['valid']) {
                $row_errors = array_merge($row_errors, $custom_validation['errors']);
            }
        }

        if (empty($row_errors)) {
            $imported_data[] = $mapped_row;
        } else {
            $errors = array_merge($errors, $row_errors);
        }
    }

    return [
        'success' => empty($errors),
        'data' => $imported_data,
        'errors' => $errors,
        'total_rows' => count($excel_data),
        'imported_rows' => count($imported_data),
        'error_rows' => count($excel_data) - count($imported_data)
    ];
}

/**
 * التحقق من صحة قيمة مستوردة
 */
function validate_import_value($value, $column_config, $row_number)
{
    if (!$column_config) {
        return ['valid' => true, 'value' => $value];
    }

    $field_name = $column_config['title'];
    $data_type = $column_config['data_type'] ?? 'text';

    // التحقق من القيم الفارغة
    if (empty($value) && isset($column_config['required']) && $column_config['required'] === true) {
        return [
            'valid' => false,
            'error' => "الحقل '{$field_name}' مطلوب في الصف {$row_number}"
        ];
    }

    if (empty($value)) {
        return ['valid' => true, 'value' => null];
    }

    // التحقق حسب نوع البيانات
    switch ($data_type) {
        case 'number':
            if (!is_numeric($value)) {
                return [
                    'valid' => false,
                    'error' => "الحقل '{$field_name}' يجب أن يكون رقماً في الصف {$row_number}"
                ];
            }
            return ['valid' => true, 'value' => (float)$value];

        case 'email':
            if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                return [
                    'valid' => false,
                    'error' => "الحقل '{$field_name}' يجب أن يكون بريد إلكتروني صحيح في الصف {$row_number}"
                ];
            }
            return ['valid' => true, 'value' => $value];

        case 'url':
            if (!filter_var($value, FILTER_VALIDATE_URL)) {
                return [
                    'valid' => false,
                    'error' => "الحقل '{$field_name}' يجب أن يكون رابط صحيح في الصف {$row_number}"
                ];
            }
            return ['valid' => true, 'value' => $value];

        case 'date':
            $date = date_create($value);
            if (!$date) {
                return [
                    'valid' => false,
                    'error' => "الحقل '{$field_name}' يجب أن يكون تاريخ صحيح في الصف {$row_number}"
                ];
            }
            return ['valid' => true, 'value' => date_format($date, 'Y-m-d')];

        case 'select':
            // التحقق من القيم المسموحة
            if (isset($column_config['import_options'])) {
                $allowed_values = array_keys($column_config['import_options']);
                if (!in_array($value, $allowed_values)) {
                    return [
                        'valid' => false,
                        'error' => "الحقل '{$field_name}' يحتوي على قيمة غير مسموحة '{$value}' في الصف {$row_number}"
                    ];
                }
            }
            return ['valid' => true, 'value' => $value];

        default:
            // نص عادي - التحقق من الطول
            if (isset($column_config['max_length']) && strlen($value) > $column_config['max_length']) {
                return [
                    'valid' => false,
                    'error' => "الحقل '{$field_name}' طويل جداً في الصف {$row_number} (الحد الأقصى: {$column_config['max_length']})"
                ];
            }
            return ['valid' => true, 'value' => $value];
    }
}

/**
 * تنزيل ملف
 */
function download_file($filepath, $filename, $delete_after = true)
{
    if (!file_exists($filepath)) {
        return false;
    }

    // تحديد نوع المحتوى
    $mime_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';

    // إعداد headers للتنزيل
    header('Content-Type: ' . $mime_type);
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Content-Length: ' . filesize($filepath));
    header('Cache-Control: must-revalidate');
    header('Pragma: public');

    // إرسال الملف
    readfile($filepath);

    // حذف الملف المؤقت
    if ($delete_after) {
        unlink($filepath);
    }

    return true;
}

/**
 * تصدير البيانات إلى CSV كبديل
 */
function export_data_to_csv($data, $columns, $config = [])
{
    try {
        $title = $config['title'] ?? 'تصدير البيانات';
        $filename = $config['filename'] ?? 'export_' . date('Y-m-d_H-i-s');

        $filepath = sys_get_temp_dir() . '/' . $filename . '.csv';
        $file = fopen($filepath, 'w');

        // إضافة BOM للدعم العربي
        fwrite($file, "\xEF\xBB\xBF");

        // إضافة رؤوس الأعمدة
        $headers = [];
        foreach ($columns as $column) {
            if (isset($column['export']) && $column['export'] === false) {
                continue;
            }
            $headers[] = $column['title'];
        }
        fputcsv($file, $headers);

        // إضافة البيانات
        foreach ($data as $row) {
            $csv_row = [];
            foreach ($columns as $column) {
                if (isset($column['export']) && $column['export'] === false) {
                    continue;
                }
                $field = $column['field'];
                $value = $row[$field] ?? '';
                $value = format_export_value($value, $column);
                $csv_row[] = $value;
            }
            fputcsv($file, $csv_row);
        }

        fclose($file);

        return [
            'success' => true,
            'filepath' => $filepath,
            'filename' => $filename . '.csv',
            'records_count' => count($data)
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * دالة تصدير ذكية - تستخدم Excel إذا كان متوفراً، وإلا CSV
 */
function smart_export_data($data, $columns, $config = [])
{
    // محاولة التصدير إلى Excel أولاً
    if (class_exists('PhpOffice\PhpSpreadsheet\Spreadsheet')) {
        return export_data_to_excel($data, $columns, $config);
    } else {
        // استخدام CSV كبديل
        return export_data_to_csv($data, $columns, $config);
    }
}
