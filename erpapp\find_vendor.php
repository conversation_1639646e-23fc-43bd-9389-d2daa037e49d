<?php
echo "<h2>البحث عن مجلد vendor</h2>";

echo "<h3>المسار الحالي:</h3>";
echo __DIR__ . "<br><br>";

echo "<h3>فحص المسارات المحتملة:</h3>";

$paths_to_check = [
    __DIR__ . '/../vendor/autoload.php',
    __DIR__ . '/../../vendor/autoload.php', 
    __DIR__ . '/../../../vendor/autoload.php',
    dirname(__DIR__) . '/vendor/autoload.php',
    dirname(__DIR__, 2) . '/vendor/autoload.php',
    dirname(__DIR__, 3) . '/vendor/autoload.php',
    '/home1/qqoshqmy/public_html/vendor/autoload.php'
];

foreach ($paths_to_check as $i => $path) {
    echo ($i + 1) . ". " . $path . " - ";
    if (file_exists($path)) {
        echo "<span style='color: green; font-weight: bold;'>موجود ✓</span>";
        
        // محاولة تحميل المكتبة
        try {
            require_once $path;
            if (class_exists('PhpOffice\PhpSpreadsheet\Spreadsheet')) {
                echo " <span style='color: blue;'>(PhpSpreadsheet متوفرة)</span>";
            }
        } catch (Exception $e) {
            echo " <span style='color: red;'>(خطأ في التحميل)</span>";
        }
    } else {
        echo "<span style='color: red;'>غير موجود ✗</span>";
    }
    echo "<br>";
}

echo "<h3>معلومات إضافية:</h3>";
echo "DOCUMENT_ROOT: " . (isset($_SERVER['DOCUMENT_ROOT']) ? $_SERVER['DOCUMENT_ROOT'] : 'غير محدد') . "<br>";
echo "SCRIPT_FILENAME: " . (isset($_SERVER['SCRIPT_FILENAME']) ? $_SERVER['SCRIPT_FILENAME'] : 'غير محدد') . "<br>";
?>
