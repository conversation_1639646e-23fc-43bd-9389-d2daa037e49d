<?php
/**
 * اختبار رفع الملفات مباشرة
 */

// تعطيل عرض الأخطاء
ini_set('display_errors', 0);
error_reporting(0);

define('BASE_PATH', __DIR__);

try {
    // تحميل النظام
    require_once 'loader.php';
    require_once 'App/Helpers/import_export_helper.php';
    
    // محاكاة رفع ملف
    echo json_encode([
        'success' => true,
        'message' => 'النظام يعمل بشكل صحيح',
        'library_status' => class_exists('PhpOffice\PhpSpreadsheet\Spreadsheet') ? 'محملة' : 'غير محملة',
        'functions_available' => [
            'read_excel_file' => function_exists('read_excel_file'),
            'export_data_to_excel' => function_exists('export_data_to_excel'),
            'create_import_template' => function_exists('create_import_template')
        ]
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
} catch (Error $e) {
    echo json_encode([
        'success' => false,
        'error' => 'خطأ فادح: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
