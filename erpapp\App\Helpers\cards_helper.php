<?php
/**
 * Cards Helper Functions
 * مساعد البطاقات والإحصائيات - نظام منفصل ومرن
 */

/**
 * عرض صفحة بطاقات موحدة
 *
 * @param array $config إعدادات الصفحة والبطاقات
 * @return void
 */
function render_cards_page($config)
{
    // إعداد المتغيرات الأساسية
    $title = $config['title'] ?? 'صفحة البطاقات';
    $module = $config['module'] ?? 'default';
    $entity = $config['entity'] ?? 'items';
    
    // البيانات الأساسية
    $stats = $config['stats'] ?? [];
    $actions = $config['actions'] ?? [];
    
    // Breadcrumb - افتراضي بسيط
    $breadcrumb = $config['breadcrumb'] ?? [
        ['title' => $title, 'active' => true]
    ];

    // عرض الصفحة
    render_cards_layout($title, $module, $entity, $stats, $breadcrumb, $actions);
}

/**
 * عرض تخطيط صفحة البطاقات
 */
function render_cards_layout($title, $module, $entity, $stats, $breadcrumb, $actions)
{
    // تحديد ما يجب عرضه
    $has_stats = !empty($stats);
    $has_actions = !empty($actions);
    
    ?>
    <div class="container-fluid">
        <!-- Header -->
        <?php render_cards_header($title, $breadcrumb); ?>

        <!-- Stats Cards -->
        <?php if ($has_stats): ?>
            <?php render_stats_cards($stats); ?>
        <?php endif; ?>

        <!-- Actions Toolbar -->
        <?php if ($has_actions): ?>
            <?php render_cards_toolbar($module, $entity, $actions); ?>
        <?php endif; ?>
    </div>

    <!-- JavaScript -->
    <?php render_cards_scripts($module, $entity); ?>
    <?php
}

/**
 * عرض رأس صفحة البطاقات
 */
function render_cards_header($title, $breadcrumb)
{
    ?>
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div class="page-header-box">
                    <!-- الصف الأول: الروابط فقط (لا توجد أزرار في cards) -->
                    <div class="header-top-row">
                        <!-- حاوي الروابط -->
                        <div class="header-breadcrumb-container">
                            <ol class="header-breadcrumb">
                                <?php foreach ($breadcrumb as $item): ?>
                                    <?php if (isset($item['url']) && !empty($item['url']) && !isset($item['active'])): ?>
                                        <li class="header-breadcrumb-item">
                                            <a href="<?= function_exists('base_url') ? base_url($item['url']) : $item['url'] ?>">
                                                <?= $item['title'] ?>
                                            </a>
                                        </li>
                                    <?php else: ?>
                                        <li class="header-breadcrumb-item active"><?= $item['title'] ?></li>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </ol>
                        </div>
                    </div>

                    <!-- الصف الثاني: عنوان الصفحة -->
                    <div class="header-title-row">
                        <h4 class="header-title"><?= $title ?></h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php
}

/**
 * عرض بطاقات الإحصائيات (الدالة الأساسية في نظام البطاقات)
 */
function render_stats_cards($stats)
{
    render_cards_stats($stats);
}

/**
 * عرض بطاقات الإحصائيات (الدالة الفعلية)
 */
function render_cards_stats($stats)
{
    ?>
    <div class="row">
        <?php foreach ($stats as $stat): ?>
            <div class="col-md-6 col-xl-3">
                <div class="card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-6">
                                <h5 class="text-muted fw-normal mt-0 text-truncate"><?= $stat['title'] ?></h5>
                                <h3 class="my-2 py-1"><?= number_format($stat['value']) ?></h3>
                            </div>
                            <div class="col-6">
                                <div class="text-end">
                                    <i class="<?= $stat['icon'] ?> text-<?= $stat['color'] ?? 'muted' ?>" style="font-size: 2rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
    <?php
}

/**
 * عرض شريط أدوات البطاقات
 */
function render_cards_toolbar($module, $entity, $actions)
{
    ?>
    <div class="row mb-3">
        <div class="col-12">
            <div class="cards-toolbar">
                <div class="toolbar-left"></div>
                <div class="toolbar-right">
                    <?php foreach ($actions as $action): ?>
                        <?php
                        $url = function_exists('base_url') ? base_url($action['url']) : $action['url'];
                        $btn_class = 'btn btn-' . ($action['type'] ?? 'secondary');
                        ?>
                        <a href="<?= $url ?>" class="<?= $btn_class ?>">
                            <i class="<?= $action['icon'] ?>"></i>
                            <span><?= $action['text'] ?></span>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
    <?php
}

/**
 * عرض JavaScript للبطاقات
 */
function render_cards_scripts($module, $entity)
{
    ?>
    <script>
    // Cards JavaScript
    document.addEventListener('DOMContentLoaded', function() {
        console.log('📊 Cards system loaded for <?php echo $module; ?>/<?php echo $entity; ?>');

        // يمكن إضافة تفاعلات للبطاقات هنا
        // مثل: تحديث البيانات، رسوم بيانية، إلخ
    });
    </script>
    <?php
}

/**
 * إنشاء بطاقة إحصائية واحدة
 */
function create_stat_card($title, $value, $icon, $color = 'primary', $description = '')
{
    return [
        'title' => $title,
        'value' => $value,
        'icon' => $icon,
        'color' => $color,
        'description' => $description
    ];
}

/**
 * عرض قسم الإجراءات السريعة
 */
function render_quick_actions_section($config)
{
    $title = $config['title'] ?? 'الإجراءات السريعة';
    $actions = $config['actions'] ?? [];

    if (empty($actions)) {
        return;
    }

    ?>
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="header-title"><?= htmlspecialchars($title) ?></h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach ($actions as $action): ?>
                            <div class="col-md-<?= $action['col_size'] ?? 3 ?>">
                                <div class="d-grid mb-2">
                                    <a href="<?= base_url($action['url']) ?>"
                                       class="btn btn-<?= $action['color'] ?? 'primary' ?>"
                                       <?= isset($action['target']) ? 'target="' . $action['target'] . '"' : '' ?>>
                                        <?php if (isset($action['icon'])): ?>
                                            <i class="<?= $action['icon'] ?> me-2"></i>
                                        <?php endif; ?>
                                        <?= htmlspecialchars($action['title']) ?>
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php
}

/**
 * عرض قسم جدول مخصص مع عنوان
 */
function render_custom_table_section($config)
{
    $title = $config['title'] ?? 'جدول البيانات';
    $columns = $config['columns'] ?? [];
    $data = $config['data'] ?? [];
    $empty_state = $config['empty_state'] ?? [
        'icon' => 'fas fa-database',
        'message' => 'لا توجد بيانات',
        'action' => null
    ];
    $footer_action = $config['footer_action'] ?? null;
    $module = $config['module'] ?? 'default';
    $entity = $config['entity'] ?? 'items';

    ?>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="header-title"><?= htmlspecialchars($title) ?></h4>
                </div>
                <div class="card-body">
                    <?php if (empty($data)): ?>
                        <div class="text-center py-4">
                            <i class="<?= $empty_state['icon'] ?> fa-3x text-muted mb-3"></i>
                            <p class="text-muted"><?= htmlspecialchars($empty_state['message']) ?></p>
                            <?php if (isset($empty_state['action']) && $empty_state['action']): ?>
                                <a href="<?= base_url($empty_state['action']['url']) ?>"
                                   class="btn btn-primary btn-sm">
                                    <?= htmlspecialchars($empty_state['action']['text']) ?>
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <?php if (function_exists('render_datatable_table')): ?>
                                <?php render_datatable_table($columns, $data, $empty_state, $module, $entity); ?>
                            <?php else: ?>
                                <div class="alert alert-warning">نظام الجداول غير متاح</div>
                            <?php endif; ?>
                        </div>

                        <?php if ($footer_action): ?>
                            <div class="text-center mt-3">
                                <a href="<?= base_url($footer_action['url']) ?>"
                                   class="btn <?= $footer_action['class'] ?? 'btn-light' ?>">
                                    <?= htmlspecialchars($footer_action['text']) ?>
                                </a>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <?php
}

/**
 * عرض صفحة داش بورد كاملة مع أقسام متعددة
 */
function render_dashboard_page($config)
{
    $title = $config['title'] ?? 'لوحة التحكم';
    $breadcrumb = $config['breadcrumb'] ?? [['title' => $title, 'active' => true]];
    $sections = $config['sections'] ?? [];

    // بدء المحتوى
    ob_start();

    // عرض الأقسام حسب النوع
    foreach ($sections as $section) {
        $section_type = $section['type'] ?? 'unknown';

        switch ($section_type) {
            case 'stats_cards':
                if (function_exists('render_stats_cards') && !empty($section['data'])) {
                    render_stats_cards($section['data']);
                }
                break;

            case 'quick_actions':
                render_quick_actions_section($section);
                break;

            case 'custom_table':
                render_custom_table_section($section);
                break;

            case 'html':
                if (isset($section['content'])) {
                    echo $section['content'];
                }
                break;

            default:
                // نوع غير معروف - تجاهل أو عرض تحذير
                break;
        }
    }

    $content = ob_get_clean();

    // عرض الصفحة باستخدام النظام البسيط
    render_page([
        'title' => $title,
        'breadcrumb' => $breadcrumb,
        'content' => $content
    ]);
}

/**
 * إنشاء مجموعة بطاقات إحصائية
 */
function create_stats_group($stats_data)
{
    $stats = [];
    foreach ($stats_data as $stat) {
        $stats[] = create_stat_card(
            $stat['title'],
            $stat['value'],
            $stat['icon'],
            $stat['color'] ?? 'primary',
            $stat['description'] ?? ''
        );
    }
    return $stats;
}

/**
 * إنشاء إجراء للبطاقات
 */
function create_card_action($type, $url, $icon, $text)
{
    return [
        'type' => $type,
        'url' => $url,
        'icon' => $icon,
        'text' => $text
    ];
}
?>
