<?php
/**
 * تشخيص مشكلة المكتبة
 */

define('BASE_PATH', __DIR__);

echo "<h2>تشخيص مكتبة PhpSpreadsheet</h2>";

echo "<h3>1. فحص المسارات المختلفة</h3>";

$paths_to_test = [
    'dirname(BASE_PATH) . "/../vendor/autoload.php"' => dirname(BASE_PATH) . '/../vendor/autoload.php',
    '__DIR__ . "/../vendor/autoload.php"' => __DIR__ . '/../vendor/autoload.php',
    'dirname(__DIR__) . "/vendor/autoload.php"' => dirname(__DIR__) . '/vendor/autoload.php',
    '"/home1/qqoshqmy/public_html/vendor/autoload.php"' => '/home1/qqoshqmy/public_html/vendor/autoload.php'
];

foreach ($paths_to_test as $description => $path) {
    echo $description . ": " . $path . " - ";
    echo (file_exists($path) ? "<span style='color: green;'>موجود ✓</span>" : "<span style='color: red;'>غير موجود ✗</span>") . "<br>";
}

// استخدام المسار الذي يعمل
$autoload_path = '/home1/qqoshqmy/public_html/vendor/autoload.php';

echo "<h3>2. تحميل المكتبة</h3>";
if (file_exists($autoload_path)) {
    require_once $autoload_path;
    echo "تم تحميل autoloader<br>";
    
    if (class_exists('PhpOffice\PhpSpreadsheet\Spreadsheet')) {
        echo "✓ مكتبة Spreadsheet متوفرة<br>";
    } else {
        echo "✗ مكتبة Spreadsheet غير متوفرة<br>";
    }
    
    if (class_exists('PhpOffice\PhpSpreadsheet\IOFactory')) {
        echo "✓ مكتبة IOFactory متوفرة<br>";
    } else {
        echo "✗ مكتبة IOFactory غير متوفرة<br>";
    }
} else {
    echo "✗ لم يتم العثور على autoloader<br>";
}

echo "<h3>3. اختبار دوال النظام</h3>";
require_once 'App/Helpers/import_export_helper.php';

try {
    check_phpspreadsheet_loaded();
    echo "✓ دالة check_phpspreadsheet_loaded تعمل<br>";
} catch (Exception $e) {
    echo "✗ خطأ في دالة check_phpspreadsheet_loaded: " . $e->getMessage() . "<br>";
}

echo "<h3>4. اختبار قراءة ملف وهمي</h3>";
// إنشاء ملف Excel بسيط للاختبار
try {
    $test_data = [['name' => 'اختبار', 'value' => '123']];
    $test_columns = [
        ['field' => 'name', 'title' => 'الاسم', 'export' => true],
        ['field' => 'value', 'title' => 'القيمة', 'export' => true]
    ];
    
    $result = export_data_to_excel($test_data, $test_columns, ['filename' => 'test']);
    
    if ($result['success']) {
        echo "✓ تم إنشاء ملف Excel للاختبار<br>";
        
        // اختبار قراءة الملف
        $read_result = read_excel_file($result['filepath']);
        
        if ($read_result['success']) {
            echo "✓ تم قراءة ملف Excel بنجاح<br>";
            echo "عدد الصفوف: " . $read_result['rows_count'] . "<br>";
        } else {
            echo "✗ فشل في قراءة ملف Excel: " . $read_result['error'] . "<br>";
        }
        
        // حذف الملف المؤقت
        if (file_exists($result['filepath'])) {
            unlink($result['filepath']);
        }
    } else {
        echo "✗ فشل في إنشاء ملف Excel: " . $result['error'] . "<br>";
    }
    
} catch (Exception $e) {
    echo "✗ خطأ في الاختبار: " . $e->getMessage() . "<br>";
}

echo "<hr>";
echo "<p><strong>إذا كانت جميع الاختبارات ناجحة، فالمشكلة في مكان آخر.</strong></p>";
?>
