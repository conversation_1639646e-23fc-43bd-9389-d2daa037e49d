<?php
/**
 * ملف تشخيص النظام
 */

echo "<h2>تشخيص نظام الاستيراد والتصدير</h2>";

echo "<h3>1. معلومات PHP</h3>";
echo "إصدار PHP: " . PHP_VERSION . "<br>";
echo "نظام التشغيل: " . PHP_OS . "<br>";
echo "الذاكرة المتاحة: " . ini_get('memory_limit') . "<br>";

echo "<h3>2. مسارات الملفات</h3>";
echo "المجلد الحالي: " . __DIR__ . "<br>";
echo "BASE_PATH: ";
if (defined('BASE_PATH')) {
    echo BASE_PATH . "<br>";
} else {
    echo "غير محدد<br>";
}

echo "<h3>3. فحص ملفات Composer</h3>";
$autoload_paths = [
    __DIR__ . '/../vendor/autoload.php',
    dirname(__DIR__) . '/vendor/autoload.php'
];

foreach ($autoload_paths as $i => $path) {
    echo "المسار " . ($i + 1) . ": " . $path . " - ";
    if (file_exists($path)) {
        echo "<span style='color: green'>موجود ✓</span><br>";
    } else {
        echo "<span style='color: red'>غير موجود ✗</span><br>";
    }
}

echo "<h3>4. فحص مكتبة PhpSpreadsheet</h3>";
$autoload_path = __DIR__ . '/../vendor/autoload.php';
if (file_exists($autoload_path)) {
    try {
        require_once $autoload_path;
        if (class_exists('PhpOffice\PhpSpreadsheet\Spreadsheet')) {
            echo "<span style='color: green'>مكتبة PhpSpreadsheet متوفرة ✓</span><br>";
        } else {
            echo "<span style='color: orange'>مكتبة PhpSpreadsheet غير متوفرة ⚠</span><br>";
        }
    } catch (Exception $e) {
        echo "<span style='color: red'>خطأ في تحميل autoloader: " . $e->getMessage() . " ✗</span><br>";
    }
} else {
    echo "<span style='color: red'>ملف autoloader غير موجود ✗</span><br>";
}

echo "<h3>5. فحص ملفات النظام</h3>";
$system_files = [
    'loader.php',
    'App/Helpers/import_export_helper.php',
    'App/Modules/Purchases/Controllers/ImportExportController.php'
];

foreach ($system_files as $file) {
    $full_path = __DIR__ . '/' . $file;
    echo $file . " - ";
    if (file_exists($full_path)) {
        echo "<span style='color: green'>موجود ✓</span><br>";
    } else {
        echo "<span style='color: red'>غير موجود ✗</span><br>";
    }
}

echo "<h3>6. اختبار دوال النظام</h3>";
try {
    define('BASE_PATH', __DIR__);
    require_once __DIR__ . '/App/Helpers/import_export_helper.php';
    
    if (function_exists('smart_export_data')) {
        echo "<span style='color: green'>دالة smart_export_data متوفرة ✓</span><br>";
    } else {
        echo "<span style='color: red'>دالة smart_export_data غير متوفرة ✗</span><br>";
    }
    
    if (function_exists('export_data_to_csv')) {
        echo "<span style='color: green'>دالة export_data_to_csv متوفرة ✓</span><br>";
    } else {
        echo "<span style='color: red'>دالة export_data_to_csv غير متوفرة ✗</span><br>";
    }
    
} catch (Exception $e) {
    echo "<span style='color: red'>خطأ في تحميل الدوال: " . $e->getMessage() . " ✗</span><br>";
}

echo "<h3>7. التوصيات</h3>";
echo "<ul>";
echo "<li>إذا كانت مكتبة PhpSpreadsheet غير متوفرة، سيتم استخدام CSV كبديل</li>";
echo "<li>تأكد من أن جميع الملفات موجودة</li>";
echo "<li>تأكد من صلاحيات الكتابة في مجلد temp</li>";
echo "</ul>";

echo "<p><strong>تاريخ التشخيص:</strong> " . date('Y-m-d H:i:s') . "</p>";
?>
