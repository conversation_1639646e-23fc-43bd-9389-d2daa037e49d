/**
 * JavaScript لنظام استيراد وتصدير CSV
 */

// متغيرات الاستيراد
let importData = {
    fileHeaders: [],
    availableColumns: [],
    mapping: {},
    uploadedFile: null
};

// فتح نافذة الاستيراد
window.openImportModal = function() {
    const modal = document.getElementById('csvImportModal');
    if (modal) {
        modal.style.display = 'flex';
        modal.classList.add('show');
        document.body.classList.add('modal-open');
        goToStep(1);
    }
};

// إغلاق نافذة الاستيراد
window.closeImportModal = function() {
    const modal = document.getElementById('csvImportModal');
    if (modal) {
        modal.style.display = 'none';
        modal.classList.remove('show');
        document.body.classList.remove('modal-open');
        resetImportModal();
    }
};

// إعادة تعيين النافذة
function resetImportModal() {
    importData = {
        fileHeaders: [],
        availableColumns: [],
        mapping: {},
        uploadedFile: null
    };
    goToStep(1);
    const csvFile = document.getElementById('csvFile');
    if (csvFile) csvFile.value = '';
    const uploadProgress = document.getElementById('uploadProgress');
    if (uploadProgress) uploadProgress.style.display = 'none';
}

// الانتقال بين الخطوات
window.goToStep = function(step) {
    // إخفاء جميع الخطوات
    document.querySelectorAll('.import-step').forEach(el => {
        el.style.display = 'none';
    });
    
    // إظهار الخطوة المطلوبة
    const stepElement = document.getElementById('step' + step);
    if (stepElement) {
        stepElement.style.display = 'block';
    }
};

// دالة رفع الملف
function uploadCsvFile(file) {
    const formData = new FormData();
    formData.append('import_file', file);

    // إظهار شريط التقدم
    const uploadProgress = document.getElementById('uploadProgress');
    if (uploadProgress) {
        uploadProgress.style.display = 'block';
        const progressBar = uploadProgress.querySelector('.progress-bar');
        if (progressBar) progressBar.style.width = '0%';
    }

    // محاكاة التقدم
    let progress = 0;
    const progressInterval = setInterval(() => {
        progress += 10;
        const progressBar = document.querySelector('.progress-bar');
        if (progressBar) progressBar.style.width = progress + '%';
        if (progress >= 90) {
            clearInterval(progressInterval);
        }
    }, 100);

    // الحصول على معلومات الوحدة والكيان من URL أو متغيرات عامة
    const currentModule = window.APP_CONFIG?.CURRENT_MODULE || 'purchases';
    const currentEntity = getCurrentEntity();

    // رفع الملف
    fetch(`${window.APP_CONFIG?.APP_URL || ''}/${currentModule}/${currentEntity}/upload`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        clearInterval(progressInterval);
        const progressBar = document.querySelector('.progress-bar');
        if (progressBar) progressBar.style.width = '100%';
        
        setTimeout(() => {
            if (uploadProgress) uploadProgress.style.display = 'none';
            
            if (data.success) {
                console.log('البيانات المستلمة:', data);
                console.log('رؤوس الأعمدة:', data.headers);
                console.log('نوع رؤوس الأعمدة:', typeof data.headers);
                console.log('هل هي مصفوفة؟', Array.isArray(data.headers));

                // التأكد من أن headers مصفوفة
                let headers = data.headers;
                if (typeof headers === 'string') {
                    console.log('تحويل النص إلى مصفوفة...');
                    headers = headers.split(',').map(h => h.trim());
                    console.log('بعد التحويل:', headers);
                }

                importData.fileHeaders = headers;
                importData.availableColumns = data.available_columns;
                const rowsCount = document.getElementById('rowsCount');
                if (rowsCount) rowsCount.textContent = data.rows_count;
                createMappingTable();
                goToStep(2);
            } else {
                alert('خطأ في رفع الملف: ' + data.error);
            }
        }, 500);
    })
    .catch(error => {
        clearInterval(progressInterval);
        if (uploadProgress) uploadProgress.style.display = 'none';
        alert('خطأ في رفع الملف: ' + error.message);
    });
}

// الحصول على اسم الكيان الحالي من URL
function getCurrentEntity() {
    const path = window.location.hash.replace('#/', '');
    const parts = path.split('/');
    if (parts.length >= 2) {
        return parts[1];
    }
    return 'suppliers'; // افتراضي
}

// إنشاء جدول تطابق الأعمدة
function createMappingTable() {
    const container = document.getElementById('mappingTable');
    if (!container) return;

    let html = '<table class="table table-bordered"><thead><tr><th>عمود الملف</th><th>حقل النظام</th></tr></thead><tbody>';

    // التأكد من أن fileHeaders مصفوفة
    let headers = importData.fileHeaders;
    console.log('نوع البيانات الأصلي:', typeof headers);
    console.log('البيانات الأصلية:', headers);

    if (typeof headers === 'string') {
        // إذا كانت نص، تحويلها إلى مصفوفة
        headers = headers.split(',').map(h => h.trim());
        console.log('بعد التحويل إلى مصفوفة:', headers);
    }

    headers.forEach((header, index) => {
        html += '<tr>';
        html += '<td><strong>' + escapeHtml(header) + '</strong></td>';
        html += '<td><select class="form-select mapping-select" data-csv-column="' + escapeHtml(header) + '">';
        html += '<option value="">-- اختر حقل --</option>';
        
        importData.availableColumns.forEach(column => {
            const selected = column.field.toLowerCase().includes(header.toLowerCase()) || 
                            header.toLowerCase().includes(column.field.toLowerCase()) ? 'selected' : '';
            html += '<option value="' + escapeHtml(column.field) + '" ' + selected + '>' + escapeHtml(column.title);
            if (column.required) html += ' *';
            html += '</option>';
        });
        
        html += '</select></td>';
        html += '</tr>';
    });
    
    html += '</tbody></table>';
    container.innerHTML = html;
}

// معاينة الاستيراد
window.previewImport = function() {
    // جمع تطابق الأعمدة
    const mapping = {};
    document.querySelectorAll('.mapping-select').forEach(select => {
        const csvColumn = select.getAttribute('data-csv-column');
        const systemField = select.value;
        if (systemField) {
            mapping[systemField] = csvColumn;
        }
    });

    console.log('تطابق الأعمدة المجمع:', mapping);
    importData.mapping = mapping;

    const currentModule = window.APP_CONFIG?.CURRENT_MODULE || 'purchases';
    const currentEntity = getCurrentEntity();

    // إرسال طلب المعاينة
    fetch(`${window.APP_CONFIG?.APP_URL || ''}/${currentModule}/${currentEntity}/preview`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ mapping: mapping })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayPreview(data);
            goToStep(3);
        } else {
            alert('خطأ في المعاينة: ' + data.error);
        }
    })
    .catch(error => {
        alert('خطأ في المعاينة: ' + error.message);
    });
};

// عرض المعاينة
function displayPreview(data) {
    const container = document.getElementById('previewContainer');
    if (!container) return;
    
    let html = '<div class="preview-info">';
    html += '<p><strong>إجمالي الصفوف:</strong> ' + data.total_rows + '</p>';
    html += '<p><strong>صفوف المعاينة:</strong> ' + data.preview_rows + '</p>';
    
    if (data.has_errors) {
        html += '<div class="alert alert-warning"><strong>تحذير:</strong> تم العثور على أخطاء في البيانات</div>';
    }
    
    html += '</div>';
    
    // جدول المعاينة
    html += '<div class="table-responsive"><table class="table table-sm table-bordered">';
    html += '<thead><tr>';
    
    // رؤوس الأعمدة
    Object.keys(importData.mapping).forEach(field => {
        const column = importData.availableColumns.find(col => col.field === field);
        html += '<th>' + escapeHtml(column ? column.title : field) + '</th>';
    });
    html += '<th>الأخطاء</th></tr></thead><tbody>';
    
    // البيانات
    data.preview_data.forEach(row => {
        html += '<tr class="' + (row.errors.length > 0 ? 'table-warning' : '') + '">';
        Object.keys(importData.mapping).forEach(field => {
            html += '<td>' + escapeHtml(row.data[field] || '-') + '</td>';
        });
        html += '<td>';
        if (row.errors.length > 0) {
            html += '<small class="text-danger">' + row.errors.join('<br>') + '</small>';
        } else {
            html += '<small class="text-success">✓</small>';
        }
        html += '</td></tr>';
    });
    
    html += '</tbody></table></div>';
    container.innerHTML = html;
}

// تنفيذ الاستيراد
window.executeImport = function() {
    if (!confirm('هل أنت متأكد من تنفيذ الاستيراد؟')) {
        return;
    }

    const currentModule = window.APP_CONFIG?.CURRENT_MODULE || 'purchases';
    const currentEntity = getCurrentEntity();

    fetch(`${window.APP_CONFIG?.APP_URL || ''}/${currentModule}/${currentEntity}/import`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ mapping: importData.mapping })
    })
    .then(response => response.json())
    .then(data => {
        displayResults(data);
        goToStep(4);
    })
    .catch(error => {
        alert('خطأ في الاستيراد: ' + error.message);
    });
};

// عرض النتائج
function displayResults(data) {
    const container = document.getElementById('resultsContainer');
    if (!container) return;
    
    let html = '<div class="results-summary">';
    
    if (data.success) {
        html += '<div class="alert alert-success">';
        html += '<h6><i class="fas fa-check-circle"></i> تم الاستيراد بنجاح!</h6>';
        html += '<p><strong>تم استيراد:</strong> ' + data.imported_rows + ' من ' + data.total_rows + ' صف</p>';
        html += '</div>';
    } else {
        html += '<div class="alert alert-danger">';
        html += '<h6><i class="fas fa-exclamation-triangle"></i> فشل الاستيراد</h6>';
        html += '<p>' + escapeHtml(data.error) + '</p>';
        html += '</div>';
    }
    
    if (data.errors && data.errors.length > 0) {
        html += '<div class="alert alert-warning">';
        html += '<h6>أخطاء الاستيراد:</h6>';
        html += '<ul>';
        data.errors.forEach(error => {
            html += '<li>' + escapeHtml(error) + '</li>';
        });
        html += '</ul>';
        html += '</div>';
    }
    
    html += '</div>';
    container.innerHTML = html;
    
    // إعادة تحميل الصفحة بعد الاستيراد الناجح
    if (data.success) {
        setTimeout(() => {
            window.location.reload();
        }, 2000);
    }
}

// دالة مساعدة لتنظيف HTML
function escapeHtml(text) {
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}

// تهيئة الأحداث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // رفع الملف
    const csvFile = document.getElementById('csvFile');
    if (csvFile) {
        csvFile.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                uploadCsvFile(file);
            }
        });
    }

    // رفع الملف بالسحب والإفلات
    const uploadArea = document.getElementById('uploadArea');
    if (uploadArea) {
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.classList.add('drag-over');
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('drag-over');
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('drag-over');
            const file = e.dataTransfer.files[0];
            if (file && file.type === 'text/csv') {
                uploadCsvFile(file);
            } else {
                alert('يرجى رفع ملف CSV فقط');
            }
        });
    }

    // إغلاق النافذة عند الضغط على زر الإغلاق
    document.querySelectorAll('[data-dismiss="modal"]').forEach(button => {
        button.addEventListener('click', function() {
            const modal = this.closest('.modal');
            if (modal) {
                modal.style.display = 'none';
                modal.classList.remove('show');
                document.body.classList.remove('modal-open');
                if (modal.id === 'csvImportModal') {
                    resetImportModal();
                }
            }
        });
    });
});
