/* ===== FORMS (بديل Bootstrap) ===== */

.form-group {
    margin-bottom: var(--spacing-4);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-2);
    font-weight: 500;
    color: var(--light-text-color);
    font-size: 14px;
}

body.dark-theme .form-label {
    color: var(--dark-text-color);
}

/* ===== FORM CONTROLS ===== */
.form-control {
    display: block;
    width: 100%;
    padding: var(--spacing-3) var(--spacing-4);
    font-size: 14px;
    line-height: 1.5;
    color: var(--light-text-color);
    background: var(--light-input-bg);
    border: 2px solid var(--light-input-border);
    border-radius: var(--border-radius);
    transition: all var(--transition-fast) ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-control:disabled {
    background: var(--gray-100);
    opacity: 0.6;
    cursor: not-allowed;
}

.form-control::placeholder {
    color: var(--gray-500);
    opacity: 1;
}

/* Dark Theme Form Controls */
body.dark-theme .form-control {
    color: var(--dark-text-color);
    background: var(--dark-input-bg);
    border-color: var(--dark-input-border);
}

body.dark-theme .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
}

body.dark-theme .form-control:disabled {
    background: var(--gray-800);
}

body.dark-theme .form-control::placeholder {
    color: var(--gray-400);
}

/* ===== SELECT ===== */
.form-select {
    display: block;
    width: 100%;
    padding: var(--spacing-3) var(--spacing-4);
    font-size: 14px;
    line-height: 1.5;
    color: var(--light-text-color);
    background: var(--light-input-bg);
    border: 2px solid var(--light-input-border);
    border-radius: var(--border-radius);
    transition: all var(--transition-fast) ease;
    cursor: pointer;
}

.form-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

body.dark-theme .form-select {
    color: var(--dark-text-color);
    background: var(--dark-input-bg);
    border-color: var(--dark-input-border);
}

body.dark-theme .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
}

/* ===== TEXTAREA ===== */
.form-textarea {
    min-height: 100px;
    resize: vertical;
}

/* ===== CHECKBOX & RADIO ===== */
.form-check {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-2);
}

.form-check-input {
    width: 18px;
    height: 18px;
    margin-inline-end: var(--spacing-2);
    border: 2px solid var(--gray-400);
    border-radius: var(--border-radius-xs);
    background: var(--light-input-bg);
    cursor: pointer;
    transition: all var(--transition-fast) ease;
}

.form-check-input:checked {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-input[type="radio"] {
    border-radius: 50%;
}

.form-check-label {
    font-size: 14px;
    color: var(--light-text-color);
    cursor: pointer;
    margin-bottom: 0;
}

body.dark-theme .form-check-input {
    border-color: var(--gray-600);
    background: var(--dark-input-bg);
}

body.dark-theme .form-check-label {
    color: var(--dark-text-color);
}

/* ===== SWITCH ===== */
.form-switch {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-2);
}

.form-switch-input {
    width: 44px;
    height: 24px;
    background: var(--gray-300);
    border-radius: 12px;
    position: relative;
    cursor: pointer;
    transition: background var(--transition-fast) ease;
    margin-inline-end: var(--spacing-2);
}

.form-switch-input::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: transform var(--transition-fast) ease;
}

.form-switch-input:checked {
    background: var(--primary-color);
}

.form-switch-input:checked::after {
    transform: translateX(20px);
}

[dir="rtl"] .form-switch-input::after {
    left: auto;
    right: 2px;
}

[dir="rtl"] .form-switch-input:checked::after {
    transform: translateX(-20px);
}

body.dark-theme .form-switch-input {
    background: var(--gray-600);
}

/* ===== INPUT GROUPS ===== */
.input-group {
    display: flex;
    width: 100%;
}

.input-group .form-control {
    border-radius: 0;
    border-right-width: 0;
}

.input-group .form-control:first-child {
    border-top-left-radius: var(--border-radius);
    border-bottom-left-radius: var(--border-radius);
}

.input-group .form-control:last-child {
    border-top-right-radius: var(--border-radius);
    border-bottom-right-radius: var(--border-radius);
    border-right-width: 2px;
}

.input-group-text {
    display: flex;
    align-items: center;
    padding: var(--spacing-3) var(--spacing-4);
    font-size: 14px;
    background: var(--gray-100);
    border: 2px solid var(--light-input-border);
    color: var(--gray-600);
}

body.dark-theme .input-group-text {
    background: var(--gray-700);
    border-color: var(--dark-input-border);
    color: var(--dark-text-color);
}

/* ===== FORM VALIDATION ===== */
.form-control.is-valid {
    border-color: var(--success-color);
}

.form-control.is-valid:focus {
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.form-control.is-invalid {
    border-color: var(--danger-color);
}

.form-control.is-invalid:focus {
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.valid-feedback {
    display: block;
    margin-top: var(--spacing-1);
    font-size: 12px;
    color: var(--success-color);
}

.invalid-feedback {
    display: block;
    margin-top: var(--spacing-1);
    font-size: 12px;
    color: var(--danger-color);
}

/* ===== FORM SIZES ===== */
.form-control-sm {
    padding: var(--spacing-2) var(--spacing-3);
    font-size: 12px;
}

.form-control-lg {
    padding: var(--spacing-4) var(--spacing-5);
    font-size: 16px;
}

/* ===== FLOATING LABELS ===== */
.form-floating {
    position: relative;
}

.form-floating .form-control {
    padding: var(--spacing-4) var(--spacing-4) var(--spacing-2) var(--spacing-4);
}

.form-floating .form-label {
    position: absolute;
    top: 0;
    left: var(--spacing-4);
    padding: var(--spacing-4) 0 0 0;
    pointer-events: none;
    transform-origin: 0 0;
    transition: all var(--transition-fast) ease;
    color: var(--gray-500);
    font-size: 14px;
}

.form-floating .form-control:focus ~ .form-label,
.form-floating .form-control:not(:placeholder-shown) ~ .form-label {
    transform: scale(0.85) translateY(-0.5rem);
    color: var(--primary-color);
}

[dir="rtl"] .form-floating .form-label {
    left: auto;
    right: var(--spacing-4);
}

/* ===== SEARCH INPUT ===== */
.search-input {
    position: relative;
}

.search-input .form-control {
    padding-left: var(--spacing-10);
}

.search-input .search-icon {
    position: absolute;
    left: var(--spacing-3);
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-500);
    pointer-events: none;
}

[dir="rtl"] .search-input .form-control {
    padding-left: var(--spacing-4);
    padding-right: var(--spacing-10);
}

[dir="rtl"] .search-input .search-icon {
    left: auto;
    right: var(--spacing-3);
}

/* ===== FORM TABS ===== */
.nav-tabs {
    display: flex;
    border-bottom: 2px solid var(--gray-200);
    margin-bottom: var(--spacing-6);
    list-style: none;
    padding: 0;
    margin-top: 0;
}

body.dark-theme .nav-tabs {
    border-bottom-color: var(--gray-700);
}

.nav-item {
    margin-bottom: -2px;
}

.nav-link {
    display: block;
    padding: var(--spacing-3) var(--spacing-5);
    text-decoration: none;
    color: var(--gray-600);
    background: transparent;
    border: 2px solid transparent;
    border-bottom: none;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    transition: all var(--transition-fast) ease;
    cursor: pointer;
    font-weight: 500;
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color);
    background: var(--gray-50);
}

.nav-link.active {
    color: var(--primary-color);
    background: var(--light-bg);
    border-color: var(--gray-200);
    border-bottom-color: var(--light-bg);
}

body.dark-theme .nav-link {
    color: var(--gray-400);
}

body.dark-theme .nav-link:hover {
    color: var(--primary-color);
    background: var(--gray-800);
}

body.dark-theme .nav-link.active {
    color: var(--primary-color);
    background: var(--dark-bg);
    border-color: var(--gray-700);
    border-bottom-color: var(--dark-bg);
}

/* Tab Content */
.tab-content {
    position: relative;
}

.tab-pane {
    display: none;
    opacity: 0;
    transition: opacity var(--transition-normal) ease;
}

.tab-pane.show {
    display: block;
}

.tab-pane.active {
    opacity: 1;
}

/* Tab Icons */
.nav-link i {
    margin-inline-end: var(--spacing-2);
    font-size: 16px;
}

/* Responsive Tabs */
@media (max-width: 768px) {
    .nav-tabs {
        flex-wrap: wrap;
    }

    .nav-link {
        padding: var(--spacing-2) var(--spacing-3);
        font-size: 14px;
    }

    .nav-link .d-none.d-md-block {
        display: none !important;
    }

    .nav-link .d-md-none.d-block {
        display: block !important;
    }
}

/* ===== FORM SECTIONS ===== */
.form-section {
    margin-bottom: var(--spacing-6);
}

.form-section-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--light-text-color);
    margin-bottom: var(--spacing-4);
    padding-bottom: var(--spacing-2);
    border-bottom: 1px solid var(--gray-200);
}

body.dark-theme .form-section-title {
    color: var(--dark-text-color);
    border-bottom-color: var(--gray-700);
}

/* ===== FORM HELP TEXT ===== */
.form-text {
    margin-top: var(--spacing-1);
    font-size: 12px;
    color: var(--gray-500);
    line-height: 1.4;
}

body.dark-theme .form-text {
    color: var(--gray-400);
}

/* ===== REQUIRED FIELD INDICATOR ===== */
.text-danger {
    color: var(--danger-color) !important;
}

/* ===== FORM LAYOUT IMPROVEMENTS ===== */
.row {
    display: flex;
    flex-wrap: wrap;
    margin-left: calc(var(--spacing-3) * -1);
    margin-right: calc(var(--spacing-3) * -1);
}

.col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
    padding-left: var(--spacing-3);
    padding-right: var(--spacing-3);
}

.col-md-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
    padding-left: var(--spacing-3);
    padding-right: var(--spacing-3);
}

.col-md-3 {
    flex: 0 0 25%;
    max-width: 25%;
    padding-left: var(--spacing-3);
    padding-right: var(--spacing-3);
}

.col-12 {
    flex: 0 0 100%;
    max-width: 100%;
    padding-left: var(--spacing-3);
    padding-right: var(--spacing-3);
}

@media (max-width: 768px) {
    .col-md-6,
    .col-md-4,
    .col-md-3 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

/* ===== READONLY FIELDS ===== */
.readonly-field {
    background-color: var(--gray-100) !important;
    border-color: var(--gray-300) !important;
    color: var(--gray-700) !important;
    cursor: not-allowed;
}

body.dark-theme .readonly-field {
    background-color: var(--gray-800) !important;
    border-color: var(--gray-600) !important;
    color: var(--gray-300) !important;
}

/* إخفاء أزرار الإضافة في وضع القراءة فقط */
.readonly-mode .btn-primary,
.readonly-mode .btn-danger {
    display: none !important;
}

/* تنسيق خاص للجداول في وضع القراءة فقط */
.readonly-mode .table th:last-child {
    display: none; /* إخفاء عمود الحذف */
}

.readonly-mode .table td:last-child {
    display: none; /* إخفاء أزرار الحذف */
}
