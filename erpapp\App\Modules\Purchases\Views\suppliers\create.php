<?php
// تضمين القالب المشترك للحصول على $form_tabs
include __DIR__ . '/form_template.php';

// إعداد الإجراءات - نفس نمط $actions في index.php
$actions = [
    [
        'type' => 'secondary',
        'url' => 'purchases/suppliers',
        'icon' => 'fas fa-arrow-left',
        'text' => 'العودة للقائمة'
    ],
    [
        'type' => 'primary',
        'form' => 'supplierForm',
        'icon' => 'fas fa-save',
        'text' => 'حفظ المورد',
        'submit' => true
    ]
];

// إعداد breadcrumb آمن - نفس نمط index.php
$safe_breadcrumb = [
    ['title' => 'المشتريات', 'url' => 'purchases'],
    ['title' => 'الموردين', 'url' => 'purchases/suppliers'],
    ['title' => 'إضافة مورد', 'active' => true]
];

// استخدام النظام الموحد - نفس طريقة render_datatable_page تماماً
render_form_page([
    'title' => $title ?? 'إضافة مورد جديد',
    'module' => 'purchases',
    'entity' => 'suppliers',
    'breadcrumb' => $safe_breadcrumb,
    'actions' => $actions,
    // النموذج بالتبويبات الديناميكية - نفس نمط $columns
    'form' => [
        'action' => base_url('purchases/suppliers/store'),
        'method' => 'POST',
        'id' => 'supplierForm',
        'tabs' => $form_tabs
    ]
]);
?>
