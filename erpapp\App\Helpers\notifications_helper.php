<?php

/**
 * نظام الإشعارات الصوتية
 * يوفر إشعارات صوتية للأحداث المختلفة في النظام
 */

/**
 * إضافة إشعار صوتي للجلسة
 */
function add_sound_notification($type, $message = null)
{
    if (!isset($_SESSION['sound_notifications'])) {
        $_SESSION['sound_notifications'] = [];
    }

    $_SESSION['sound_notifications'][] = [
        'type' => $type,
        'message' => $message,
        'timestamp' => time()
    ];
}

/**
 * الحصول على الإشعارات الصوتية وحذفها من الجلسة
 */
function get_sound_notifications()
{
    $notifications = $_SESSION['sound_notifications'] ?? [];
    unset($_SESSION['sound_notifications']);
    return $notifications;
}

/**
 * إشعار صوتي للنجاح
 */
function sound_success($message = null)
{
    add_sound_notification('success', $message);
}

/**
 * إشعار صوتي للخطأ
 */
function sound_error($message = null)
{
    add_sound_notification('error', $message);
}

/**
 * إشعار صوتي للتحذير
 */
function sound_warning($message = null)
{
    add_sound_notification('warning', $message);
}

/**
 * إشعار صوتي للمعلومات
 */
function sound_info($message = null)
{
    add_sound_notification('info', $message);
}

/**
 * إشعار صوتي مخصص
 */
function sound_custom($sound_file, $message = null)
{
    add_sound_notification('custom', $message, $sound_file);
}

/**
 * عرض مشغل الإشعارات الصوتية
 */
function render_sound_notifications()
{
    $notifications = get_sound_notifications();
    
    if (empty($notifications)) {
        return '';
    }

    $output = '<div id="soundNotifications" style="display: none;">';
    
    foreach ($notifications as $notification) {
        $soundFile = get_sound_file($notification['type']);
        $output .= '<audio preload="auto" data-type="' . $notification['type'] . '">';
        $output .= '<source src="' . base_url('public/sounds/' . $soundFile) . '" type="audio/mpeg">';
        $output .= '<source src="' . base_url('public/sounds/' . str_replace('.mp3', '.ogg', $soundFile)) . '" type="audio/ogg">';
        $output .= '</audio>';
    }
    
    $output .= '</div>';
    
    // إضافة JavaScript لتشغيل الأصوات
    $output .= '<script>';
    $output .= 'document.addEventListener("DOMContentLoaded", function() {';
    $output .= '    playNotificationSounds();';
    $output .= '});';
    $output .= '</script>';
    
    return $output;
}

/**
 * الحصول على ملف الصوت حسب النوع
 */
function get_sound_file($type)
{
    $sounds = [
        'success' => 'success.mp3',
        'error' => 'error.mp3',
        'warning' => 'warning.mp3',
        'info' => 'info.mp3',
        'create' => 'create.mp3',
        'update' => 'update.mp3',
        'delete' => 'delete.mp3',
        'save' => 'save.mp3',
        'notification' => 'notification.mp3'
    ];

    return $sounds[$type] ?? 'notification.mp3';
}

/**
 * إشعارات صوتية للعمليات الأساسية
 */
function sound_create($message = 'تم الإنشاء بنجاح')
{
    add_sound_notification('create', $message);
}

function sound_update($message = 'تم التحديث بنجاح')
{
    add_sound_notification('update', $message);
}

function sound_delete($message = 'تم الحذف بنجاح')
{
    add_sound_notification('delete', $message);
}

function sound_save($message = 'تم الحفظ بنجاح')
{
    add_sound_notification('save', $message);
}

/**
 * تحقق من تفعيل الإشعارات الصوتية للمستخدم
 */
function is_sound_enabled()
{
    // يمكن ربطها بإعدادات المستخدم لاحقاً
    return $_SESSION['sound_enabled'] ?? true;
}

/**
 * تفعيل/إلغاء تفعيل الإشعارات الصوتية
 */
function toggle_sound_notifications($enabled = true)
{
    $_SESSION['sound_enabled'] = $enabled;
}
